#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.CPU_DCache=Enabled
CORTEX_M7.CPU_ICache=Enabled
CORTEX_M7.IPParameters=CPU_ICache,CPU_DCache
DMA2D.ColorMode=DMA2D_OUTPUT_RGB565
DMA2D.IPParameters=ColorMode
FMC.CASLatency2=FMC_SDRAM_CAS_LATENCY_3
FMC.ColumnBitsNumber2=FMC_SDRAM_COLUMN_BITS_NUM_9
FMC.ExitSelfRefreshDelay2=9
FMC.IPParameters=ColumnBitsNumber2,CASLatency2,SDClockPeriod2,SDClockPeriod1,ReadPipeDelay2,ReadPipeDelay1,ReadBurst2,ReadBurst1,LoadToActiveDelay2,ExitSelfRefreshDelay2,SelfRefreshTime2,Row<PERSON>ycleD<PERSON>y2,RowCycleD<PERSON>y1,WriteRecoveryTime2,RPD<PERSON>y2,R<PERSON><PERSON>y1,RCDD<PERSON>y2
FMC.LoadToActiveDelay2=2
FMC.RCDDelay2=3
FMC.RPDelay1=3
FMC.RPDelay2=3
FMC.ReadBurst1=FMC_SDRAM_RBURST_ENABLE
FMC.ReadBurst2=FMC_SDRAM_RBURST_ENABLE
FMC.ReadPipeDelay1=FMC_SDRAM_RPIPE_DELAY_1
FMC.ReadPipeDelay2=FMC_SDRAM_RPIPE_DELAY_1
FMC.RowCycleDelay1=8
FMC.RowCycleDelay2=8
FMC.SDClockPeriod1=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SDClockPeriod2=FMC_SDRAM_CLOCK_PERIOD_2
FMC.SelfRefreshTime2=6
FMC.WriteRecoveryTime2=3
File.Version=6
I2C1.I2C_Speed_Mode=I2C_Fast
I2C1.IPParameters=Timing,I2C_Speed_Mode
I2C1.Timing=0x6000030D
KeepUserPlacement=false
LTDC.ActiveW=800
LTDC.Alpha_L0=255
LTDC.FBStartAdress_L0=0xD0000000
LTDC.HBP=46
LTDC.HFP=40
LTDC.HSync=1
LTDC.IPParameters=HSync,HBP,ActiveW,HFP,VSync,VBP,VFP,Layers,PixelFormat_L0,WindowX1_L0,WindowY1_L0,Alpha_L0,FBStartAdress_L0,ImageWidth_L0,ImageHeight_L0
LTDC.ImageHeight_L0=480
LTDC.ImageWidth_L0=800
LTDC.Layers=0
LTDC.PixelFormat_L0=LTDC_PIXEL_FORMAT_RGB565
LTDC.VBP=23
LTDC.VFP=13
LTDC.VSync=3
LTDC.WindowX1_L0=800
LTDC.WindowY1_L0=480
Mcu.CPN=STM32F767IGT6
Mcu.Family=STM32F7
Mcu.IP0=CORTEX_M7
Mcu.IP1=DMA2D
Mcu.IP2=FMC
Mcu.IP3=I2C1
Mcu.IP4=LTDC
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SYS
Mcu.IPNb=8
Mcu.Name=STM32F767I(G-I)Tx
Mcu.Package=LQFP176
Mcu.Pin0=PE3
Mcu.Pin1=PI9
Mcu.Pin10=PH0/OSC_IN
Mcu.Pin11=PH1/OSC_OUT
Mcu.Pin12=PC0
Mcu.Pin13=PA3
Mcu.Pin14=PB0
Mcu.Pin15=PB1
Mcu.Pin16=PF11
Mcu.Pin17=PF12
Mcu.Pin18=PF13
Mcu.Pin19=PF14
Mcu.Pin2=PI10
Mcu.Pin20=PF15
Mcu.Pin21=PG0
Mcu.Pin22=PG1
Mcu.Pin23=PE7
Mcu.Pin24=PE8
Mcu.Pin25=PE9
Mcu.Pin26=PE10
Mcu.Pin27=PE11
Mcu.Pin28=PE12
Mcu.Pin29=PE13
Mcu.Pin3=PF0
Mcu.Pin30=PE14
Mcu.Pin31=PE15
Mcu.Pin32=PH6
Mcu.Pin33=PH7
Mcu.Pin34=PD8
Mcu.Pin35=PD9
Mcu.Pin36=PD10
Mcu.Pin37=PD14
Mcu.Pin38=PD15
Mcu.Pin39=PG2
Mcu.Pin4=PF1
Mcu.Pin40=PG4
Mcu.Pin41=PG5
Mcu.Pin42=PG6
Mcu.Pin43=PG7
Mcu.Pin44=PG8
Mcu.Pin45=PC7
Mcu.Pin46=PA10
Mcu.Pin47=PA11
Mcu.Pin48=PA12
Mcu.Pin49=PA13
Mcu.Pin5=PF2
Mcu.Pin50=PH13
Mcu.Pin51=PH15
Mcu.Pin52=PI0
Mcu.Pin53=PI2
Mcu.Pin54=PA14
Mcu.Pin55=PD0
Mcu.Pin56=PD1
Mcu.Pin57=PD7
Mcu.Pin58=PG10
Mcu.Pin59=PG11
Mcu.Pin6=PF3
Mcu.Pin60=PG15
Mcu.Pin61=PB6
Mcu.Pin62=PB7
Mcu.Pin63=PB8
Mcu.Pin64=PB9
Mcu.Pin65=PE0
Mcu.Pin66=PE1
Mcu.Pin67=VP_DMA2D_VS_DMA2D
Mcu.Pin68=VP_SYS_VS_Systick
Mcu.Pin7=PF4
Mcu.Pin8=PF5
Mcu.Pin9=PF10
Mcu.PinsNb=69
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F767IGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.LTDC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA10.GPIO_PuPd=GPIO_PULLUP
PA10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA10.Mode=RGB565
PA10.Signal=LTDC_B4
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA11.GPIO_PuPd=GPIO_PULLUP
PA11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA11.Locked=true
PA11.Mode=RGB565
PA11.Signal=LTDC_R4
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA12.Locked=true
PA12.Mode=RGB565
PA12.Signal=LTDC_R5
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA3.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PA3.Mode=RGB565
PA3.Signal=LTDC_B5
PB0.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB0.GPIO_PuPd=GPIO_PULLUP
PB0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB0.Mode=RGB565
PB0.Signal=LTDC_R3
PB1.GPIOParameters=GPIO_Speed,GPIO_PuPd
PB1.GPIO_PuPd=GPIO_PULLUP
PB1.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB1.Mode=RGB565
PB1.Signal=LTDC_R6
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_FM8
PB8.GPIO_FM8=SYSCFG_PMC_I2C_PB8_FMP
PB8.GPIO_PuPd=GPIO_PULLUP
PB8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB8.Mode=RGB565
PB8.Signal=LTDC_B6
PB9.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_FM9
PB9.GPIO_FM9=SYSCFG_PMC_I2C_PB9_FMP
PB9.GPIO_PuPd=GPIO_PULLUP
PB9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB9.Mode=RGB565
PB9.Signal=LTDC_B7
PC0.Signal=FMC_SDNWE
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC7.Locked=true
PC7.Mode=RGB565
PC7.Signal=LTDC_G6
PD0.Signal=FMC_D2_DA2
PD1.Signal=FMC_D3_DA3
PD10.Signal=FMC_D15_DA15
PD14.Signal=FMC_D0_DA0
PD15.Signal=FMC_D1_DA1
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Signal=FMC_D13_DA13
PD9.Signal=FMC_D14_DA14
PE0.Signal=FMC_NBL0
PE1.Signal=FMC_NBL1
PE10.Signal=FMC_D7_DA7
PE11.Signal=FMC_D8_DA8
PE12.Signal=FMC_D9_DA9
PE13.Signal=FMC_D10_DA10
PE14.Signal=FMC_D11_DA11
PE15.Signal=FMC_D12_DA12
PE3.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultEXTI
PE3.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING
PE3.GPIO_PuPd=GPIO_NOPULL
PE3.Locked=true
PE3.Signal=GPXTI3
PE7.Signal=FMC_D4_DA4
PE8.Signal=FMC_D5_DA5
PE9.Signal=FMC_D6_DA6
PF0.Signal=FMC_A0
PF1.Signal=FMC_A1
PF10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PF10.GPIO_PuPd=GPIO_PULLUP
PF10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PF10.Mode=RGB565
PF10.Signal=LTDC_DE
PF11.Signal=FMC_SDNRAS
PF12.Signal=FMC_A6
PF13.Signal=FMC_A7
PF14.Signal=FMC_A8
PF15.Signal=FMC_A9
PF2.Signal=FMC_A2
PF3.Signal=FMC_A3
PF4.Signal=FMC_A4
PF5.Signal=FMC_A5
PG0.Signal=FMC_A10
PG1.Signal=FMC_A11
PG10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG10.GPIO_PuPd=GPIO_PULLUP
PG10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG10.Locked=true
PG10.Mode=RGB565
PG10.Signal=LTDC_G3
PG11.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG11.GPIO_PuPd=GPIO_PULLUP
PG11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG11.Locked=true
PG11.Mode=RGB565
PG11.Signal=LTDC_B3
PG15.Signal=FMC_SDNCAS
PG2.Signal=FMC_A12
PG4.Signal=FMC_A14_BA0
PG5.Signal=FMC_A15_BA1
PG6.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG6.GPIO_PuPd=GPIO_PULLUP
PG6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG6.Mode=RGB565
PG6.Signal=LTDC_R7
PG7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PG7.GPIO_PuPd=GPIO_PULLUP
PG7.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PG7.Mode=RGB565
PG7.Signal=LTDC_CLK
PG8.Signal=FMC_SDCLK
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PH13.GPIOParameters=GPIO_Speed,GPIO_PuPd
PH13.GPIO_PuPd=GPIO_PULLUP
PH13.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH13.Locked=true
PH13.Mode=RGB565
PH13.Signal=LTDC_G2
PH15.GPIOParameters=GPIO_Speed,GPIO_PuPd
PH15.GPIO_PuPd=GPIO_PULLUP
PH15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH15.Locked=true
PH15.Mode=RGB565
PH15.Signal=LTDC_G4
PH6.Mode=SdramChipSelect2_2
PH6.Signal=FMC_SDNE1
PH7.Mode=SdramChipSelect2_2
PH7.Signal=FMC_SDCKE1
PI0.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI0.GPIO_PuPd=GPIO_PULLUP
PI0.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI0.Locked=true
PI0.Mode=RGB565
PI0.Signal=LTDC_G5
PI10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI10.GPIO_PuPd=GPIO_PULLUP
PI10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI10.Mode=RGB565
PI10.Signal=LTDC_HSYNC
PI2.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI2.GPIO_PuPd=GPIO_PULLUP
PI2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI2.Mode=RGB565
PI2.Signal=LTDC_G7
PI9.GPIOParameters=GPIO_Speed,GPIO_PuPd
PI9.GPIO_PuPd=GPIO_PULLUP
PI9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PI9.Mode=RGB565
PI9.Signal=LTDC_VSYNC
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F767IGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F7 V1.17.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=AP3216C.ioc
ProjectManager.ProjectName=AP3216C
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=CMake
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA2D_Init-DMA2D-false-HAL-true,4-MX_FMC_Init-FMC-false-HAL-true,5-MX_LTDC_Init-LTDC-false-HAL-true,6-MX_I2C1_Init-I2C1-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.AHBFreq_Value=*********
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=********
RCC.APB1TimFreq_Value=*********
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=*********
RCC.APB2TimFreq_Value=*********
RCC.CECFreq_Value=32786.88524590164
RCC.CortexFreq_Value=*********
RCC.DFSDMAudioFreq_Value=96000000
RCC.DFSDMFreq_Value=*********
RCC.EthernetFreq_Value=*********
RCC.FCLKCortexFreq_Value=*********
RCC.FamilyName=M
RCC.HCLKFreq_Value=*********
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=********
RCC.I2C2Freq_Value=********
RCC.I2C3Freq_Value=********
RCC.I2C4Freq_Value=********
RCC.I2SFreq_Value=96000000
RCC.IPParameters=AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CECFreq_Value,CortexFreq_Value,DFSDMAudioFreq_Value,DFSDMFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LCDTFTFreq_Value,LPTIM1Freq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLI2SPCLKFreq_Value,PLLI2SQCLKFreq_Value,PLLI2SRCLKFreq_Value,PLLI2SRoutputFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLQoutputFreq_Value,PLLRFreq_Value,PLLSAIPCLKFreq_Value,PLLSAIQCLKFreq_Value,PLLSAIR,PLLSAIRCLKFreq_Value,PLLSAIoutputFreq_Value,PLLSourceVirtual,RNGFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMC2Freq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,UART7Freq_Value,UART8Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USART6Freq_Value,USBFreq_Value,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value
RCC.LCDTFTFreq_Value=32000000
RCC.LPTIM1Freq_Value=********
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=*********
RCC.PLLCLKFreq_Value=*********
RCC.PLLI2SPCLKFreq_Value=96000000
RCC.PLLI2SQCLKFreq_Value=96000000
RCC.PLLI2SRCLKFreq_Value=96000000
RCC.PLLI2SRoutputFreq_Value=96000000
RCC.PLLM=25
RCC.PLLN=432
RCC.PLLQCLKFreq_Value=*********
RCC.PLLQoutputFreq_Value=*********
RCC.PLLRFreq_Value=*********
RCC.PLLSAIPCLKFreq_Value=96000000
RCC.PLLSAIQCLKFreq_Value=96000000
RCC.PLLSAIR=3
RCC.PLLSAIRCLKFreq_Value=64000000
RCC.PLLSAIoutputFreq_Value=96000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RNGFreq_Value=*********
RCC.SAI1Freq_Value=96000000
RCC.SAI2Freq_Value=96000000
RCC.SDMMC2Freq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=96000000
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=********
RCC.UART5Freq_Value=********
RCC.UART7Freq_Value=********
RCC.UART8Freq_Value=********
RCC.USART1Freq_Value=*********
RCC.USART2Freq_Value=********
RCC.USART3Freq_Value=********
RCC.USART6Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCOI2SOutputFreq_Value=*********
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=*********
RCC.VCOSAIOutputFreq_Value=*********
SH.FMC_A0.0=FMC_A0,13b-sda2
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1,13b-sda2
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10,13b-sda2
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11,13b-sda2
SH.FMC_A11.ConfNb=1
SH.FMC_A12.0=FMC_A12,13b-sda2
SH.FMC_A12.ConfNb=1
SH.FMC_A14_BA0.0=FMC_BA0,FourSdramBanks2
SH.FMC_A14_BA0.ConfNb=1
SH.FMC_A15_BA1.0=FMC_BA1,FourSdramBanks2
SH.FMC_A15_BA1.ConfNb=1
SH.FMC_A2.0=FMC_A2,13b-sda2
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3,13b-sda2
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4,13b-sda2
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5,13b-sda2
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6,13b-sda2
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7,13b-sda2
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8,13b-sda2
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9,13b-sda2
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0,sd-16b-d2
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10,sd-16b-d2
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11,sd-16b-d2
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12,sd-16b-d2
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13,sd-16b-d2
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14,sd-16b-d2
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15,sd-16b-d2
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1,sd-16b-d2
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2,sd-16b-d2
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3,sd-16b-d2
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4,sd-16b-d2
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5,sd-16b-d2
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6,sd-16b-d2
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7,sd-16b-d2
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8,sd-16b-d2
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9,sd-16b-d2
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0,Sd2ByteEnable2
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1,Sd2ByteEnable2
SH.FMC_NBL1.ConfNb=1
SH.FMC_SDCLK.0=FMC_SDCLK,13b-sda2
SH.FMC_SDCLK.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS,13b-sda2
SH.FMC_SDNCAS.ConfNb=1
SH.FMC_SDNRAS.0=FMC_SDNRAS,13b-sda2
SH.FMC_SDNRAS.ConfNb=1
SH.FMC_SDNWE.0=FMC_SDNWE,13b-sda2
SH.FMC_SDNWE.ConfNb=1
SH.GPXTI3.0=GPIO_EXTI3
SH.GPXTI3.ConfNb=1
VP_DMA2D_VS_DMA2D.Mode=DMA2D_Activate
VP_DMA2D_VS_DMA2D.Signal=DMA2D_VS_DMA2D
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
