# AP3216C 传感器驱动说明

## 概述

AP3216C 是一款集成环境光传感器（ALS）、接近传感器（PS）和红外发射器（IR LED）的三合一传感器芯片。本驱动基于 STM32F767IGT6 开发，提供完整的传感器控制、数据读取和处理功能。

## 硬件配置

### STM32F767IGT6 配置
- **主频**: 216MHz
- **I2C接口**: I2C1，快速模式（400kHz）
- **I2C引脚**:
  - PB6: I2C1_SCL
  - PB7: I2C1_SDA
- **中断引脚**: PE3（EXTI3，无上拉下拉，上升沿触发）

### AP3216C 传感器规格
- **I2C地址**: 0x1E（7位地址）/ 0x3C（8位地址）
- **工作电压**: 2.4V - 3.6V（本项目使用3.3V）
- **I2C速度**: 支持标准模式（100kHz）和快速模式（400kHz）
- **功能**:
  - 环境光传感器（ALS）：16位数据，支持4种量程
  - 接近传感器（PS）：10位数据，带对象检测标志
  - 红外LED：10位数据

## 文件结构

```
BSP/
├── Inc/
│   ├── AP3216C.h          # AP3216C驱动头文件
│   └── AP3216C_Test.h     # AP3216C测试头文件（已集成到主文件）
└── Src/
    ├── AP3216C.c          # AP3216C驱动实现文件（包含测试代码）
    └── AP3216C_Test.c     # 独立测试文件（可选）
```

## 代码架构优化

### 1. 状态管理
- 使用 `AP3216C_StatusTypeDef` 枚举统一错误处理
- 所有函数返回明确的状态码
- 参数验证和错误检查

### 2. 数据结构
- `AP3216C_RawDataTypeDef`: 原始传感器数据
- `AP3216C_ProcessedDataTypeDef`: 处理后的数据和描述
- `AP3216C_ConfigTypeDef`: 传感器配置参数

### 3. 功能分层
- **基础层**: 寄存器读写、初始化、模式设置
- **数据层**: 原始数据读取、数据转换
- **处理层**: 数据分析、状态判断、高级功能
- **测试层**: 条件编译的测试代码

## 主要功能

### 1. 基本操作
```c
AP3216C_StatusTypeDef AP3216C_Init(void);           // 初始化传感器
AP3216C_StatusTypeDef AP3216C_Reset(void);          // 软件复位
AP3216C_StatusTypeDef AP3216C_SetMode(mode);        // 设置工作模式
```

### 2. 数据读取
```c
AP3216C_StatusTypeDef AP3216C_ReadRawData(raw_data);     // 读取所有原始数据
float AP3216C_ReadALS(void);                             // 读取环境光数据
uint16_t AP3216C_ReadPS(void);                           // 读取接近传感器数据
uint16_t AP3216C_ReadIR(void);                           // 读取红外数据
```

### 3. 阈值配置
```c
AP3216C_StatusTypeDef AP3216C_SetALSThreshold(low, high);  // 设置环境光阈值
AP3216C_StatusTypeDef AP3216C_SetPSThreshold(low, high);   // 设置接近传感器阈值
```

### 4. 数据处理
```c
const char* AP3216C_GetLightLevelString(lux_value);        // 获取光照等级描述
const char* AP3216C_GetProximityString(ps_data);           // 获取接近状态描述
float AP3216C_CalculateDistance(ps_data);                  // 计算距离估算
float AP3216C_CalculateIRIntensity(ir_data);               // 计算红外强度百分比
```

### 5. 高级功能
```c
AP3216C_StatusTypeDef AP3216C_GetEnvironmentLevel(lux, level);      // 获取环境等级数值
AP3216C_StatusTypeDef AP3216C_DetectMotion(ps_data, motion);        // 运动检测
AP3216C_StatusTypeDef AP3216C_CalculateAverageData(avg_data, samples); // 平均数据计算
```

### 6. 中断处理
```c
void AP3216C_EXTI3_IRQHandler(void);           // PE3外部中断处理
uint8_t AP3216C_ProcessInterrupt(void);        // 主循环中处理中断
uint8_t AP3216C_CheckIntPin(void);             // 检查中断引脚状态
```

## 使用方法

### 1. 基本初始化

```c
#include "AP3216C.h"

// 初始化传感器
AP3216C_StatusTypeDef status = AP3216C_Init();
if (status != AP3216C_OK) {
    // 处理初始化错误
}
```

### 2. 数据读取和处理

```c
// 方法1：读取处理后的完整数据（推荐）
AP3216C_ProcessedDataTypeDef sensor_data;
if (AP3216C_ProcessAllData(&sensor_data) == AP3216C_OK) {
    printf("环境光: %.1f Lux (%s)\n", 
           sensor_data.als_lux, sensor_data.light_level);
    printf("接近状态: %s\n", sensor_data.proximity_status);
    printf("估算距离: %.1f cm\n", sensor_data.distance_cm);
    printf("红外强度: %.1f%%\n", sensor_data.ir_intensity);
}

// 方法2：读取原始数据
AP3216C_RawDataTypeDef raw_data;
if (AP3216C_ReadRawData(&raw_data) == AP3216C_OK) {
    // 处理原始数据
}

// 方法3：单独读取各传感器数据
float als_lux = AP3216C_ReadALS();
uint16_t ps_data = AP3216C_ReadPS();
uint16_t ir_data = AP3216C_ReadIR();
```

### 3. 高级数据处理

```c
// 环境等级检测
uint8_t env_level;
AP3216C_GetEnvironmentLevel(sensor_data.als_lux, &env_level);

// 运动检测
uint8_t motion_detected;
AP3216C_DetectMotion(sensor_data.ps_raw, &motion_detected);

// 平均数据计算
AP3216C_ProcessedDataTypeDef avg_data;
AP3216C_CalculateAverageData(&avg_data, 10); // 10次采样平均
```

### 4. 中断使用

```c
// 在主循环中处理中断
if (AP3216C_ProcessInterrupt()) {
    // 处理中断事件
    uint8_t int_status = AP3216C_GetINTStatus();
    if (int_status & 0x01) {
        // ALS中断
    }
    if (int_status & 0x02) {
        // PS中断
    }
}
```

## 测试功能

### 启用测试代码
在 `AP3216C.h` 中设置：
```c
#define AP3216C_ENABLE_TEST     1   // 启用测试代码
```

### 运行测试
```c
#if AP3216C_ENABLE_TEST
AP3216C_TEST();  // 运行完整的LCD测试界面
#endif
```

### 测试功能特性
- **实时数据显示**: 所有传感器数据实时更新
- **状态监控**: 中断状态、引脚状态、运动检测
- **数据处理验证**: 距离计算、强度百分比、环境等级
- **错误处理测试**: 初始化失败、读取错误处理
- **性能监控**: 测试计数、运行时间显示

## 寄存器映射

| 地址 | 名称 | 描述 |
|------|------|------|
| 0x00 | SYS_CONFIG | 系统配置寄存器 |
| 0x01 | INT_STATUS | 中断状态寄存器 |
| 0x02 | INT_CLR | 中断清除寄存器 |
| 0x0A | IR_DATA_L | IR数据低字节 |
| 0x0B | IR_DATA_H | IR数据高字节 |
| 0x0C | ALS_DATA_L | ALS数据低字节 |
| 0x0D | ALS_DATA_H | ALS数据高字节 |
| 0x0E | PS_DATA_L | PS数据低字节（4位） |
| 0x0F | PS_DATA_H | PS数据高字节（6位） |

## 工作模式

| 模式值 | 描述 |
|--------|------|
| 0x00 | 掉电模式 |
| 0x01 | ALS功能激活 |
| 0x02 | PS功能激活 |
| 0x03 | ALS + PS功能激活 |
| 0x04 | 软件复位 |
| 0x07 | ALS + PS + IR功能激活 |

## 优化特性

1. **统一错误处理**: 所有函数返回状态码，便于错误追踪
2. **突发读取**: 一次读取所有数据寄存器，保证数据一致性
3. **非阻塞中断**: 中断服务程序只设置标志位
4. **参数验证**: 所有公共函数进行参数有效性检查
5. **配置管理**: 统一的配置结构体管理传感器参数
6. **条件编译**: 测试代码可选编译，减少发布版本代码量

## 性能指标

- **I2C通信速度**: 400kHz
- **数据更新频率**: 最高10Hz（受I2C速度限制）
- **中断响应时间**: < 1ms
- **内存占用**: 
  - 代码: ~8KB（包含测试代码）
  - RAM: ~200字节（静态变量）

## 故障排除

### 1. 初始化失败
- 检查I2C连接（PB6/PB7）
- 确认电源电压（3.3V）
- 验证I2C地址（0x3C）
- 检查I2C时钟配置（400kHz）

### 2. 数据读取错误
- 确认传感器已正确初始化
- 检查工作模式设置
- 验证I2C通信状态
- 增加启动延时时间

### 3. 中断不工作
- 确认PE3引脚配置（EXTI3）
- 检查中断使能设置
- 验证中断回调函数
- 确认阈值设置正确

### 4. 测试界面异常
- 确认LCD已正确初始化
- 检查LCD显示函数可用性
- 验证测试代码编译开关
- 检查内存是否充足

## 示例代码

完整的使用示例请参考：
- `Core/Src/main.c` - 主程序集成示例
- `AP3216C.c` 中的 `AP3216C_TEST()` 函数 - 完整测试示例

## 版本历史

- **v2.0**: 重构代码架构，添加状态管理和高级数据处理
- **v1.0**: 基础功能实现，参考野火开发板示例