/**
  ******************************************************************************
  * @file    AP3216C_Test_Example.c
  * <AUTHOR>
  * @brief   AP3216C传感器测试示例代码
  ******************************************************************************
  * @attention
  *
  * 这是一个简单的测试示例，展示如何使用优化后的AP3216C驱动
  * 
  * 使用方法：
  * 1. 在 main.c 中包含 "AP3216C.h"
  * 2. 在主函数中调用相应的测试函数
  *
  ******************************************************************************
  */

#include "AP3216C.h"
#include "lcd.h"
#include <stdio.h>

/**
 * @brief 简单的传感器数据读取测试
 * @param None
 * @retval None
 */
void AP3216C_SimpleTest(void)
{
    float als_data;
    uint16_t ps_data, ir_data;
    
    // 初始化传感器
    AP3216C_Init();
    
    // 等待传感器稳定
    HAL_Delay(100);
    
    // 读取传感器数据
    als_data = AP3216C_ReadALS();
    ps_data = AP3216C_ReadPS();
    ir_data = AP3216C_ReadIR();
    
    // 通过串口输出数据（如果有串口调试）
    printf("AP3216C Sensor Data:\n");
    printf("ALS: %.1f Lux\n", als_data);
    printf("PS: %d\n", ps_data);
    printf("IR: %d\n", ir_data);
}

/**
 * @brief 在主函数中调用的示例
 * @param None
 * @retval None
 * @note 将此代码添加到 main.c 的主循环中
 */
void main_example(void)
{
    /* 系统初始化代码... */
    
    // 初始化LCD（如果需要显示）
    LCD_Init();
    
    // 方法1: 运行完整的LCD测试（推荐）
    #if AP3216C_ENABLE_TEST
        AP3216C_TEST();  // 这将进入无限循环显示传感器数据
    #endif
    
    // 方法2: 简单的数据读取测试
    // AP3216C_SimpleTest();
    
    /* 主循环... */
    while(1)
    {
        // 其他应用代码...
        HAL_Delay(100);
    }
}

/**
 * @brief 传感器数据处理示例
 * @param None
 * @retval None
 */
void AP3216C_DataProcessingExample(void)
{
    float als_data;
    uint16_t ps_data, ir_data;
    
    // 初始化传感器
    AP3216C_Init();
    
    while(1)
    {
        // 读取传感器数据
        als_data = AP3216C_ReadALS();
        ps_data = AP3216C_ReadPS();
        ir_data = AP3216C_ReadIR();
        
        // 环境光等级判断
        if (als_data < 10) {
            printf("环境光: 很暗\n");
        } else if (als_data < 100) {
            printf("环境光: 暗\n");
        } else if (als_data < 500) {
            printf("环境光: 正常\n");
        } else if (als_data < 1000) {
            printf("环境光: 明亮\n");
        } else {
            printf("环境光: 很明亮\n");
        }
        
        // 接近传感器数据处理
        if (ps_data == 55555) {
            printf("接近传感器: 光线太强，数据无效\n");
        } else {
            uint16_t proximity_value = ps_data & 0x7FFF;  // 去除最高位
            uint8_t object_detected = (ps_data & 0x8000) ? 1 : 0;  // 最高位表示对象检测
            
            printf("接近传感器: %d, 对象: %s\n", 
                   proximity_value, 
                   object_detected ? "接近" : "远离");
        }
        
        // 红外传感器数据
        printf("红外传感器: %d\n", ir_data);
        
        printf("------------------------\n");
        
        // 延时1秒
        HAL_Delay(1000);
    }
}

/**
 * @brief 阈值设置示例
 * @param None
 * @retval None
 */
void AP3216C_ThresholdExample(void)
{
    // 初始化传感器
    AP3216C_Init();
    
    // 设置自定义阈值
    AP3216C_Set_ALS_Threshold(50, 800);    // 环境光阈值: 50~800 Lux
    AP3216C_Set_PS_Threshold(100, 300);    // 接近传感器阈值: 100~300
    
    printf("阈值设置完成\n");
    printf("环境光阈值: 50~800 Lux\n");
    printf("接近传感器阈值: 100~300\n");
    
    // 检查中断状态
    while(1)
    {
        uint8_t int_status = AP3216C_Get_INTStatus();
        
        if (int_status != 0) {
            printf("中断状态: 0x%02X\n", int_status);
            
            if (int_status & 0x01) {
                printf("环境光传感器中断触发\n");
            }
            
            if (int_status & 0x02) {
                printf("接近传感器中断触发\n");
            }
        }
        
        HAL_Delay(500);
    }
}

/* 使用说明:
 * 
 * 1. 在 main.c 中添加:
 *    #include "AP3216C.h"
 * 
 * 2. 在主函数中选择一种测试方式:
 *    
 *    // 方式1: 完整的LCD测试界面
 *    AP3216C_TEST();
 *    
 *    // 方式2: 简单的数据读取
 *    AP3216C_SimpleTest();
 *    
 *    // 方式3: 数据处理示例
 *    AP3216C_DataProcessingExample();
 *    
 *    // 方式4: 阈值设置示例
 *    AP3216C_ThresholdExample();
 * 
 * 3. 硬件连接:
 *    - VCC: 3.3V
 *    - GND: GND
 *    - SCL: PB6 (I2C1_SCL)
 *    - SDA: PB7 (I2C1_SDA)
 *    - INT: PE3 (可选，用于中断)
 * 
 * 4. 注意事项:
 *    - 确保I2C1已正确初始化
 *    - 传感器地址为0x3C (8位地址)
 *    - 复位后需要延时至少10ms
 *    - 强光环境下接近传感器可能返回55555
 */
