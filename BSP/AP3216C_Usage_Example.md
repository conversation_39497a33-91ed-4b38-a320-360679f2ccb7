# AP3216C 传感器驱动使用说明 (简化版)

## 概述

本驱动是 AP3216C 三合一传感器的简化版本，参考野火开发板实现，保留了核心功能：
- 环境光传感器 (ALS) - 测量环境光强度
- 接近传感器 (PS) - 检测物体接近
- 红外传感器 (IR) - 测量红外光强度

## 主要特点

### 简化的代码结构
- 移除了复杂的状态管理和高级处理函数
- 保留核心的初始化、数据读取和转换功能
- 代码更易理解和维护

### 核心函数
1. **初始化函数**
   - `AP3216C_Init()` - 初始化传感器

2. **数据读取函数**
   - `AP3216C_ReadALS()` - 读取环境光数据 (Lux)
   - `AP3216C_ReadPS()` - 读取接近传感器数据
   - `AP3216C_ReadIR()` - 读取红外传感器数据

3. **配置函数**
   - `AP3216C_Set_ALS_Threshold()` - 设置环境光阈值
   - `AP3216C_Set_PS_Threshold()` - 设置接近传感器阈值
   - `AP3216C_Get_INTStatus()` - 获取中断状态

## 使用方法

### 1. 基本初始化

```c
#include "AP3216C.h"

int main(void)
{
    // 系统初始化...
    
    // 初始化 AP3216C 传感器
    AP3216C_Init();
    
    // 主循环...
}
```

### 2. 读取传感器数据

```c
void read_sensor_data(void)
{
    float als_lux;
    uint16_t ps_data, ir_data;
    
    // 读取环境光数据 (单位: Lux)
    als_lux = AP3216C_ReadALS();
    
    // 读取接近传感器数据
    ps_data = AP3216C_ReadPS();
    
    // 读取红外传感器数据
    ir_data = AP3216C_ReadIR();
    
    // 处理数据...
    printf("ALS: %.1f Lux\n", als_lux);
    printf("PS: %d\n", ps_data & 0x7FFF);
    printf("IR: %d\n", ir_data);
}
```

### 3. 接近传感器数据解析

```c
void parse_proximity_data(uint16_t ps_data)
{
    if (ps_data == 55555) {
        printf("PS: Light too strong\n");
    } else {
        uint16_t proximity_value = ps_data & 0x7FFF;  // 去除最高位
        uint8_t object_detected = (ps_data & 0x8000) ? 1 : 0;  // 最高位表示对象检测
        
        printf("PS Value: %d, Object: %s\n", 
               proximity_value, 
               object_detected ? "Near" : "Far");
    }
}
```

### 4. 环境光等级判断

```c
const char* get_light_level(float als_lux)
{
    if (als_lux < 10)
        return "Very Dark";
    else if (als_lux < 100)
        return "Dark";
    else if (als_lux < 500)
        return "Normal";
    else if (als_lux < 1000)
        return "Bright";
    else
        return "Very Bright";
}
```

## 测试代码

驱动包含完整的测试代码，可以在 LCD 上显示传感器数据：

```c
// 在 main.c 中调用测试函数
#if AP3216C_ENABLE_TEST
    AP3216C_TEST();  // 这将进入无限循环显示传感器数据
#endif
```

测试界面显示内容：
- 传感器初始化状态
- 实时环境光数据 (Lux)
- 实时接近传感器数据和状态
- 实时红外传感器数据
- 环境光等级描述

## 硬件连接

### I2C 连接
- SCL: PB6 (I2C1_SCL)
- SDA: PB7 (I2C1_SDA)
- VCC: 3.3V
- GND: GND

### 中断引脚 (可选)
- INT: PE3 (EXTI3)

## 注意事项

1. **初始化延时**: 传感器复位后必须延时至少 10ms
2. **I2C 地址**: 使用 8 位地址 0x3C (7位地址 0x1E 左移一位)
3. **光线干扰**: 强光环境下接近传感器可能返回 55555 表示无效
4. **数据更新频率**: 建议读取间隔不少于 200ms

## 与原版本的区别

### 简化内容
- 移除了复杂的状态枚举和错误处理
- 移除了高级数据处理函数 (平均值计算、运动检测等)
- 移除了复杂的配置结构体
- 简化了函数返回值 (大部分函数返回 void)

### 保留内容
- 核心的传感器初始化和数据读取功能
- 基本的阈值设置功能
- 完整的测试代码和 LCD 显示
- 与现有 I2C 驱动的兼容性

这个简化版本更适合快速集成和学习使用，代码结构清晰，易于理解和修改。
