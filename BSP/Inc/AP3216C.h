/**
  ******************************************************************************
  * @file    AP3216C.h
  * <AUTHOR>
  * @brief   AP3216C环境光和接近传感器驱动头文件 (简化版)
  ******************************************************************************
  * @attention
  *
  * AP3216C是一个三合一传感器，包含：
  * - 环境光传感器 (ALS)
  * - 接近传感器 (PS)
  * - 红外LED (IR)
  *
  * I2C地址: 0x1E (7位) / 0x3C (8位)
  * 支持I2C快速模式 (400kHz)
  *
  * 参考野火开发板实现，保留核心功能
  *
  ******************************************************************************
  */

#ifndef __AP3216C_H__
#define __AP3216C_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 包含文件 --------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include <stdio.h>
#include <string.h>

/* 宏定义 ----------------------------------------------------------------------*/
#define AP3216C_ENABLE_TEST             1       // 启用测试代码

/* AP3216C I2C地址 */
#define AP3216C_ADDRESS                 0x3C    // 8位地址 (0x1E << 1)

/* AP3216C寄存器地址 */
#define AP3216C_SYS_CONFIG              0x00    // 系统配置寄存器
#define AP3216C_INT_STATUS              0x01    // 中断状态寄存器
#define AP3216C_INT_CLEAR_MANNER        0x02    // 中断清除方式寄存器
#define AP3216C_IR_DATA_LOW             0x0A    // 红外数据低字节
#define AP3216C_IR_DATA_HIGH            0x0B    // 红外数据高字节
#define AP3216C_ALS_DATA_LOW            0x0C    // 环境光数据低字节
#define AP3216C_ALS_DATA_HIGH           0x0D    // 环境光数据高字节
#define AP3216C_PS_DATA_LOW             0x0E    // 接近传感器数据低字节
#define AP3216C_PS_DATA_HIGH            0x0F    // 接近传感器数据高字节
#define AP3216C_ALS_CONFIG              0x10    // 环境光配置寄存器
#define AP3216C_ALS_LOW_THRESHOLD7_0    0x1A    // 环境光低阈值低字节
#define AP3216C_ALS_LOW_THRESHOLD15_8   0x1B    // 环境光低阈值高字节
#define AP3216C_ALS_HIGH_THRESHOLD7_0   0x1C    // 环境光高阈值低字节
#define AP3216C_ALS_HIGH_THRESHOLD15_8  0x1D    // 环境光高阈值高字节
#define AP3216C_PS_LOW_THRESHOLD2_0     0x2A    // 接近传感器低阈值低字节
#define AP3216C_PS_LOW_THRESHOLD10_3    0x2B    // 接近传感器低阈值高字节
#define AP3216C_PS_HIGH_THRESHOLD2_0    0x2C    // 接近传感器高阈值低字节
#define AP3216C_PS_HIGH_THRESHOLD10_3   0x2D    // 接近传感器高阈值高字节

/* AP3216C工作模式 */
#define AP3216C_MODE_POWER_DOWN         0x00    // 断电模式
#define AP3216C_MODE_ALS                0x01    // 仅环境光传感器
#define AP3216C_MODE_PS                 0x02    // 仅接近传感器
#define AP3216C_MODE_ALS_AND_PS         0x03    // 环境光和接近传感器
#define AP3216C_MODE_SW_RESET           0x04    // 软件复位

/* AP3216C环境光传感器量程 */
#define AP3216C_ALS_RANGE_20661         0x00    // 20661 Lux
#define AP3216C_ALS_RANGE_5162          0x01    // 5162 Lux
#define AP3216C_ALS_RANGE_1291          0x02    // 1291 Lux
#define AP3216C_ALS_RANGE_323           0x03    // 323 Lux

/* 中断引脚配置 */
#define AP_INT_GPIO_PORT                GPIOE
#define AP_INT_GPIO_PIN                 GPIO_PIN_3
#define AP_INT_Read()                   HAL_GPIO_ReadPin(AP_INT_GPIO_PORT, AP_INT_GPIO_PIN)

/* 延时定义 */
#define Delay(ms)                       HAL_Delay(ms)

/* 函数声明 --------------------------------------------------------------------*/

/* 基础函数 */
void AP3216C_Init(void);
void AP3216C_Reset(void);
void AP3216C_Set_ALS_Threshold(uint16_t low_threshold, uint16_t high_threshold);
void AP3216C_Set_PS_Threshold(uint16_t low_threshold, uint16_t high_threshold);
uint8_t AP3216C_Get_INTStatus(void);

/* 数据读取函数 */
float AP3216C_ReadALS(void);
uint16_t AP3216C_ReadPS(void);
uint16_t AP3216C_ReadIR(void);

/* 测试函数 */
#if AP3216C_ENABLE_TEST
void AP3216C_TEST(void);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __AP3216C_H__ */