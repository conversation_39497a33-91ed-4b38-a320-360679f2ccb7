/**
  ******************************************************************************
  * @file    AP3216C.h
  * <AUTHOR>
  * @brief   AP3216C环境光和接近传感器驱动头文件
  ******************************************************************************
  * @attention
  *
  * AP3216C是一个三合一传感器，包含：
  * - 环境光传感器 (ALS)
  * - 接近传感器 (PS)
  * - 红外LED (IR)
  *
  * I2C地址: 0x1E (7位) / 0x3C (8位)
  * 支持I2C快速模式 (400kHz)
  *
  ******************************************************************************
  */

#ifndef __AP3216C_H__
#define __AP3216C_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 头文件包含 ------------------------------------------------------------------*/
#include "main.h"
#include "i2c.h"
#include <stdio.h>
#include <string.h>

/* 测试代码编译控制 ------------------------------------------------------------*/
#define AP3216C_ENABLE_TEST     1   // 1:启用测试代码, 0:禁用测试代码

/* 中断引脚定义 ----------------------------------------------------------------*/
#define AP_INT_GPIO_PORT            GPIOE
#define AP_INT_GPIO_PIN             GPIO_PIN_3
#define AP_INT_Read()               HAL_GPIO_ReadPin(AP_INT_GPIO_PORT, AP_INT_GPIO_PIN)

/* AP3216C I2C地址 -------------------------------------------------------------*/
#define AP3216C_ADDRESS             0x3C    // 8位I2C地址 (0x1E << 1)
#define AP3216C_WHO_AM_I            0x75    // 设备ID寄存器

/* AP3216C寄存器地址 -----------------------------------------------------------*/
#define AP3216C_SYS_CONFIG          0x00    // 系统配置寄存器
#define AP3216C_INT_STATUS          0x01    // 中断状态寄存器
#define AP3216C_INT_CLEAR_MANNER    0x02    // 中断清除寄存器
#define AP3216C_IR_DATA_LOW         0x0A    // IR数据低字节
#define AP3216C_IR_DATA_HIGH        0x0B    // IR数据高字节
#define AP3216C_ALS_DATA_LOW        0x0C    // ALS数据低字节
#define AP3216C_ALS_DATA_HIGH       0x0D    // ALS数据高字节
#define AP3216C_PS_DATA_LOW         0x0E    // PS数据低字节
#define AP3216C_PS_DATA_HIGH        0x0F    // PS数据高字节

/* ALS配置寄存器 ---------------------------------------------------------------*/
#define AP3216C_ALS_CONFIG          0x10    // ALS配置寄存器
#define AP3216C_ALS_CALIBRAE        0x19    // ALS校准寄存器
#define AP3216C_ALS_LOW_THRESHOLD7_0    0x1A    // ALS低阈值[7:0]
#define AP3216C_ALS_LOW_THRESHOLD15_8   0x1B    // ALS低阈值[15:8]
#define AP3216C_ALS_HIGH_THRESHOLD7_0   0x1C    // ALS高阈值[7:0]
#define AP3216C_ALS_HIGH_THRESHOLD15_8  0x1D    // ALS高阈值[15:8]

/* PS配置寄存器 ----------------------------------------------------------------*/
#define AP3216C_PS_CONFIG           0x20    // PS配置寄存器
#define AP3216C_PS_LED_CTRL         0x21    // PS LED控制寄存器
#define AP3216C_PS_INT_FORM         0x22    // PS中断形式寄存器
#define AP3216C_PS_MEAN_TIME        0x23    // PS平均时间寄存器
#define AP3216C_PS_LED_WAITING_TIME 0x24    // PS LED等待时间寄存器
#define AP3216C_PS_CALIBRATION_L    0x28    // PS校准低字节
#define AP3216C_PS_CALIBRATION_H    0x29    // PS校准高字节
#define AP3216C_PS_LOW_THRESHOLD2_0     0x2A    // PS低阈值[2:0]
#define AP3216C_PS_LOW_THRESHOLD10_3    0x2B    // PS低阈值[10:3]
#define AP3216C_PS_HIGH_THRESHOLD2_0    0x2C    // PS高阈值[2:0]
#define AP3216C_PS_HIGH_THRESHOLD10_3   0x2D    // PS高阈值[10:3]

/* 状态定义 -------------------------------------------------------------------*/
typedef enum {
    AP3216C_OK = 0,
    AP3216C_ERROR,
    AP3216C_TIMEOUT,
    AP3216C_INVALID_PARAM
} AP3216C_StatusTypeDef;

/* AP3216C工作模式 -------------------------------------------------------------*/
typedef enum {
    AP3216C_MODE_POWER_DOWN = 0,    // 掉电模式 (默认)
    AP3216C_MODE_ALS,               // ALS功能激活
    AP3216C_MODE_PS,                // PS+IR功能激活
    AP3216C_MODE_ALS_AND_PS,        // ALS和PS+IR功能激活
    AP3216C_MODE_SW_RESET,          // 软件复位
    AP3216C_MODE_ALS_ONCE,          // ALS功能单次
    AP3216C_MODE_PS_ONCE,           // PS+IR功能单次
    AP3216C_MODE_ALS_AND_PS_ONCE,   // ALS和PS+IR功能单次
} AP3216C_ModeTypeDef;

/* ALS量程设置 -----------------------------------------------------------------*/
typedef enum {
    AP3216C_ALS_RANGE_20661 = 0,    // 分辨率 = 0.35 lux/count (默认)
    AP3216C_ALS_RANGE_5162,         // 分辨率 = 0.0788 lux/count
    AP3216C_ALS_RANGE_1291,         // 分辨率 = 0.0197 lux/count
    AP3216C_ALS_RANGE_323,          // 分辨率 = 0.0049 lux/count
} AP3216C_ALSRangeTypeDef;

/* 原始数据结构 ----------------------------------------------------------------*/
typedef struct {
    uint16_t als_raw;           // ALS原始数据
    uint16_t ps_raw;            // PS原始数据
    uint16_t ir_raw;            // IR原始数据
    uint8_t int_status;         // 中断状态
    uint8_t int_pin_state;      // 中断引脚状态
} AP3216C_RawDataTypeDef;

/* 处理后数据结构 --------------------------------------------------------------*/
typedef struct {
    float als_lux;              // 环境光值 (Lux)
    uint16_t ps_raw;            // 接近传感器原始数据
    uint16_t ir_raw;            // 红外原始数据
    const char* light_level;    // 环境光等级描述
    const char* proximity_status; // 接近状态描述
    float distance_cm;          // 估算距离 (cm)
    float ir_intensity;         // 红外强度百分比
    uint8_t int_status;         // 中断状态
    uint8_t int_pin_state;      // 中断引脚状态
} AP3216C_ProcessedDataTypeDef;

/* 传感器配置结构 --------------------------------------------------------------*/
typedef struct {
    AP3216C_ModeTypeDef mode;           // 工作模式
    AP3216C_ALSRangeTypeDef als_range;  // ALS量程
    uint16_t als_low_threshold;         // ALS低阈值
    uint16_t als_high_threshold;        // ALS高阈值
    uint16_t ps_low_threshold;          // PS低阈值
    uint16_t ps_high_threshold;         // PS高阈值
} AP3216C_ConfigTypeDef;

/* 基础函数声明 ----------------------------------------------------------------*/
AP3216C_StatusTypeDef AP3216C_Init(void);
AP3216C_StatusTypeDef AP3216C_Reset(void);
AP3216C_StatusTypeDef AP3216C_SetMode(AP3216C_ModeTypeDef mode);
AP3216C_StatusTypeDef AP3216C_SetConfig(AP3216C_ConfigTypeDef *config);

/* 阈值设置函数 ----------------------------------------------------------------*/
AP3216C_StatusTypeDef AP3216C_SetALSThreshold(uint16_t low_threshold, uint16_t high_threshold);
AP3216C_StatusTypeDef AP3216C_SetPSThreshold(uint16_t low_threshold, uint16_t high_threshold);

/* 数据读取函数 ----------------------------------------------------------------*/
AP3216C_StatusTypeDef AP3216C_ReadRawData(AP3216C_RawDataTypeDef *raw_data);
float AP3216C_ReadALS(void);
uint16_t AP3216C_ReadPS(void);
uint16_t AP3216C_ReadIR(void);
uint8_t AP3216C_GetINTStatus(void);

/* 中断处理函数 ----------------------------------------------------------------*/
void AP3216C_EXTI3_IRQHandler(void);
uint8_t AP3216C_ProcessInterrupt(void);
uint8_t AP3216C_CheckIntPin(void);

/* 数据处理函数 ----------------------------------------------------------------*/
const char* AP3216C_GetLightLevelString(float lux_value);
const char* AP3216C_GetProximityString(uint16_t ps_data);
float AP3216C_CalculateDistance(uint16_t ps_data);
float AP3216C_CalculateIRIntensity(uint16_t ir_data);
AP3216C_StatusTypeDef AP3216C_ProcessAllData(AP3216C_ProcessedDataTypeDef *processed_data);

/* 高级数据处理函数 ------------------------------------------------------------*/
AP3216C_StatusTypeDef AP3216C_GetEnvironmentLevel(float lux_value, uint8_t *level);
AP3216C_StatusTypeDef AP3216C_DetectMotion(uint16_t ps_data, uint8_t *motion_detected);
AP3216C_StatusTypeDef AP3216C_CalculateAverageData(AP3216C_ProcessedDataTypeDef *avg_data, uint8_t samples);

/* 测试函数声明 ----------------------------------------------------------------*/
#if AP3216C_ENABLE_TEST
void AP3216C_TEST(void);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __AP3216C_H__ */