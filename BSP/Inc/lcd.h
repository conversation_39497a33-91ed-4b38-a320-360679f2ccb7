
#ifndef INC_LCD_H_
#define INC_LCD_H_

#include "main.h"
#include "ltdc.h"

/* ========================================================================================
 * LCD显示驱动配置
 * ======================================================================================== */

/**
 * @brief LCD多层显示功能开关
 * @note  取消注释以下宏定义来启用多层显示模式
 *        - 未定义: 单层模式，仅使用LTDC层0进行显示
 *        - 已定义: 多层模式，支持层0和层1的Alpha混合显示
 */
//#define LCD_MULTI_LAYER_ENABLE

/* 条件编译配置 */
#ifdef LCD_MULTI_LAYER_ENABLE
    #define LCD_LAYER_COUNT         2       // 支持的层数量
    #define LCD_DEFAULT_LAYER       0       // 默认操作层
    #define LCD_LAYER_0             0       // 背景层
    #define LCD_LAYER_1             1       // 前景层
#else
    #define LCD_LAYER_COUNT         1       // 仅支持单层
    #define LCD_DEFAULT_LAYER       0       // 默认操作层
    #define LCD_LAYER_0             0       // 唯一层
#endif

/* 硬件相关定义 */
extern LTDC_HandleTypeDef hltdc;  // 使用HAL层定义的LTDC句柄

/**
 * @brief LCD设备结构体
 * @note  包含LCD显示相关的所有配置参数
 */
typedef struct
{
    uint32_t panel_width;       // 面板物理宽度
    uint32_t panel_height;      // 面板物理高度
    uint16_t hsync_width;       // 水平同步宽度
    uint16_t vsync_width;       // 垂直同步宽度
    uint16_t h_back_porch;      // 水平后沿
    uint16_t v_back_porch;      // 垂直后沿
    uint16_t h_front_porch;     // 水平前沿
    uint16_t v_front_porch;     // 垂直前沿
    uint8_t  active_layer;      // 当前活动层
    uint8_t  orientation;       // 显示方向
    uint16_t width;             // 有效显示宽度
    uint16_t height;            // 有效显示高度
    uint32_t pixel_size;        // 像素大小(字节)
} lcd_device_t;

extern lcd_device_t g_lcd_device;
extern uint32_t g_point_color;
extern uint32_t g_back_color;

/* 像素格式定义 */
#define LCD_PIXEL_FORMAT_RGB565         0x02
#define LCD_PIXEL_FORMAT               LCD_PIXEL_FORMAT_RGB565

/* 常用颜色定义 */
#define LCD_COLOR_WHITE                0xFFFF
#define LCD_COLOR_BLACK                0x0000
#define LCD_COLOR_BLUE                 0x001F
#define LCD_COLOR_RED                  0xF800
#define LCD_COLOR_GREEN                0x07E0
#define LCD_COLOR_YELLOW               0xFFE0
#define LCD_COLOR_CYAN                 0x07FF
#define LCD_COLOR_MAGENTA              0xF81F
#define LCD_COLOR_GRAY                 0x7BEF
#define LCD_COLOR_LIGHT_GRAY           0xBDF7
#define LCD_COLOR_DARK_GRAY            0x39E7

/* 显示参数定义 */
#define LCD_PANEL_WIDTH                800     // 面板宽度
#define LCD_PANEL_HEIGHT               480     // 面板高度
#define LCD_PIXEL_SIZE                 2       // RGB565像素大小
#define LCD_FRAME_BUFFER_SIZE          (LCD_PANEL_WIDTH * LCD_PANEL_HEIGHT * LCD_PIXEL_SIZE)

/* 错误代码定义 */
#define LCD_OK                         0       // 操作成功
#define LCD_ERROR                      1       // 一般错误
#define LCD_ERROR_INVALID_PARAM        2       // 参数错误
#define LCD_ERROR_OUT_OF_BOUNDS        3       // 坐标越界
#define LCD_ERROR_LAYER_NOT_SUPPORTED  4       // 不支持的层


/* ========================================================================================
 * 函数声明
 * ======================================================================================== */

/* 初始化和配置函数 */
/**
 * @brief  初始化LCD参数（由LTDC初始化调用）
 * @param  None
 * @retval None
 */
void LTDC_ParameterInit(void);

/**
 * @brief  初始化LCD显示器
 * @param  None
 * @retval LCD_OK: 初始化成功, LCD_ERROR: 初始化失败
 */
uint8_t LCD_Init(void);

/**
 * @brief  设置显示方向
 * @param  orientation: 显示方向 (0: 竖屏, 1: 横屏)
 * @retval LCD_OK: 设置成功, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_SetOrientation(uint8_t orientation);

/**
 * @brief  开启或关闭LCD显示
 * @param  enable: 1-开启显示, 0-关闭显示
 * @retval None
 */
void LCD_DisplaySwitch(uint8_t enable);

/* 基础绘图函数 */
/**
 * @brief  在指定位置绘制一个像素点
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 像素颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawPixel(uint16_t x, uint16_t y, uint32_t color);

/**
 * @brief  读取指定位置的像素颜色
 * @param  x: X坐标
 * @param  y: Y坐标
 * @retval 像素颜色值，如果坐标越界返回0
 */
uint32_t LCD_ReadPixel(uint16_t x, uint16_t y);

/**
 * @brief  绘制直线
 * @param  x1: 起点X坐标
 * @param  y1: 起点Y坐标
 * @param  x2: 终点X坐标
 * @param  y2: 终点Y坐标
 * @param  color: 线条颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);

/**
 * @brief  绘制矩形
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 矩形边框颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);

/**
 * @brief  绘制圆形
 * @param  x0: 圆心X坐标
 * @param  y0: 圆心Y坐标
 * @param  radius: 圆半径
 * @param  color: 圆形颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawCircle(uint16_t x0, uint16_t y0, uint8_t radius, uint32_t color);

/**
 * @brief  绘制填充圆形
 * @param  x0: 圆心X坐标
 * @param  y0: 圆心Y坐标
 * @param  radius: 圆半径
 * @param  color: 填充颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillCircle(uint16_t x0, uint16_t y0, uint8_t radius, uint32_t color);

/* 填充函数 */
/**
 * @brief  用指定颜色填充矩形区域
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 填充颜色
 * @retval LCD_OK: 填充成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillRect(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);

/**
 * @brief  用颜色数组填充矩形区域
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color_array: 颜色数组指针
 * @retval LCD_OK: 填充成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_FillRectWithArray(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t *color_array);

/**
 * @brief  清屏
 * @param  color: 清屏颜色
 * @retval None
 */
void LCD_Clear(uint32_t color);

/* 文本显示函数 */
/**
 * @brief  显示单个字符
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  character: 要显示的字符
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  mode: 显示模式 (0: 非叠加模式, 1: 叠加模式)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowChar(uint16_t x, uint16_t y, uint8_t character, uint8_t font_size, uint8_t mode);

/**
 * @brief  显示数字
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  number: 要显示的数字
 * @param  length: 显示长度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowNumber(uint16_t x, uint16_t y, uint32_t number, uint8_t length, uint8_t font_size);

/**
 * @brief  显示数字(扩展模式)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  number: 要显示的数字
 * @param  length: 显示长度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  mode: 显示模式 (bit7: 0-不填充前导0, 1-填充前导0; bit0: 0-非叠加, 1-叠加)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowNumberEx(uint16_t x, uint16_t y, uint32_t number, uint8_t length, uint8_t font_size, uint8_t mode);

/**
 * @brief  在指定区域内显示字符串
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  width: 显示区域宽度
 * @param  height: 显示区域高度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  string: 要显示的字符串
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t font_size, uint8_t *string);

/**
 * @brief  显示字符串(简化版本)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  string: 要显示的字符串
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowStringSimple(uint16_t x, uint16_t y, uint8_t font_size, char *string);

/* 颜色设置函数 */
/**
 * @brief  同时设置前景色和背景色
 * @param  foreground_color: 前景色（画笔颜色）
 * @param  background_color: 背景色
 * @retval None
 * @note   这个函数在单层和多层模式下都可以使用
 */
void LCD_SetColor(uint32_t foreground_color, uint32_t background_color);

/**
 * @brief  设置前景色
 * @param  color: 前景色
 * @retval None
 */
void LCD_SetForegroundColor(uint32_t color);

/**
 * @brief  设置背景色
 * @param  color: 背景色
 * @retval None
 */
void LCD_SetBackgroundColor(uint32_t color);

/**
 * @brief  获取前景色
 * @retval 当前前景色
 */
uint32_t LCD_GetForegroundColor(void);

/**
 * @brief  获取背景色
 * @retval 当前背景色
 */
uint32_t LCD_GetBackgroundColor(void);

#ifdef LCD_MULTI_LAYER_ENABLE
/* 多层显示函数 (仅在多层模式下可用) */
/**
 * @brief  切换当前操作层
 * @param  layer: 目标层 (0 或 1)
 * @retval LCD_OK: 切换成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_SwitchLayer(uint8_t layer);

/**
 * @brief  清除指定层
 * @param  layer: 目标层 (0 或 1)
 * @param  color: 清除颜色
 * @retval LCD_OK: 清除成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_ClearLayer(uint8_t layer, uint32_t color);

/**
 * @brief  在指定层填充矩形区域
 * @param  layer: 目标层 (0 或 1)
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 填充颜色
 * @retval LCD_OK: 填充成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillRectLayer(uint8_t layer, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);

/**
 * @brief  在指定层绘制像素点
 * @param  layer: 目标层 (0 或 1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 像素颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawPixelLayer(uint8_t layer, uint16_t x, uint16_t y, uint32_t color);

/**
 * @brief  设置层的透明度
 * @param  layer: 目标层 (0 或 1)
 * @param  alpha: 透明度值 (0-255, 0为完全透明, 255为完全不透明)
 * @retval LCD_OK: 设置成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_SetLayerAlpha(uint8_t layer, uint8_t alpha);

/**
 * @brief  启用或禁用指定层
 * @param  layer: 目标层 (0 或 1)
 * @param  enable: 1-启用, 0-禁用
 * @retval LCD_OK: 设置成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_EnableLayer(uint8_t layer, uint8_t enable);
#endif /* LCD_MULTI_LAYER_ENABLE */

/* 兼容性函数 (保持与旧版本的兼容性) */
#define LCD_DrawPoint(x, y)                     LCD_DrawPixel(x, y, g_point_color)
#define LCD_ReadPoint(x, y)                     LCD_ReadPixel(x, y)
#define LCD_Fast_DrawPoint(x, y, color)         LCD_DrawPixel(x, y, color)
#define LCD_Fill(x1, y1, x2, y2, color)         LCD_FillRect(x1, y1, x2, y2, color)
#define LCD_Color_Fill(x1, y1, x2, y2, colors)  LCD_FillRectWithArray(x1, y1, x2, y2, colors)
#define LCD_Draw_Circle(x0, y0, r)              LCD_DrawCircle(x0, y0, r, g_point_color)
#define LCD_Fill_Circle(x0, y0, r, color)       LCD_FillCircle(x0, y0, r, color)
#define LCD_ShowChar(x, y, c, s, m)             LCD_ShowChar(x, y, c, s, m)
#define LCD_ShowNum(x, y, n, l, s)              LCD_ShowNumber(x, y, n, l, s)
#define LCD_ShowxNum(x, y, n, l, s, m)          LCD_ShowNumberEx(x, y, n, l, s, m)
#define LCD_ShowStr(x, y, s, p)                 LCD_ShowStringSimple(x, y, s, p)

/* 颜色兼容性定义 */
#define WHITE                                   LCD_COLOR_WHITE
#define BLACK                                   LCD_COLOR_BLACK
#define BLUE                                    LCD_COLOR_BLUE
#define RED                                     LCD_COLOR_RED
#define GREEN                                   LCD_COLOR_GREEN
#define YELLOW                                  LCD_COLOR_YELLOW

/* 全局变量兼容性定义 */
#define POINT_COLOR                             g_point_color
#define BACK_COLOR                              g_back_color
#define lcdltdc                                 g_lcd_device
#define LTDC_Handler                            hltdc

#endif /* INC_LCD_H_ */