/**
  ******************************************************************************
  * @file    AP3216C.c
  * <AUTHOR>
  * @brief   AP3216C环境光和接近传感器驱动实现 (简化版)
  ******************************************************************************
  * @attention
  *
  * AP3216C是一个三合一传感器，包含：
  * - 环境光传感器 (ALS)
  * - 接近传感器 (PS) 
  * - 红外LED (IR)
  *
  * I2C地址: 0x1E (7位) / 0x3C (8位)
  * 支持I2C快速模式 (400kHz)
  * 参考野火STM32F767开发板实现，简化代码结构
  *
  ******************************************************************************
  */

#include "AP3216C.h"
#include "lcd.h"

/* 私有函数声明 ----------------------------------------------------------------*/
static void AP3216C_WriteReg(uint8_t reg_add, uint8_t reg_dat);
static void AP3216C_ReadData(uint8_t reg_add, unsigned char* Read, uint8_t num);
static void AP3216C_INT_Config(void);

/**
 * @brief 写数据到AP3216C寄存器
 * @param reg_add: 寄存器地址
 * @param reg_data: 要写入的数据
 * @retval None
 */
static void AP3216C_WriteReg(uint8_t reg_add, uint8_t reg_dat)
{
    HAL_I2C_Mem_Write(&hi2c1, AP3216C_ADDRESS, reg_add, I2C_MEMADD_SIZE_8BIT, &reg_dat, 1, 1000);
}

/**
 * @brief 从AP3216C寄存器读取数据
 * @param reg_add: 寄存器地址
 * @param Read: 存储数据的缓冲区
 * @param num: 要读取的字节数
 * @retval None
 */
static void AP3216C_ReadData(uint8_t reg_add, unsigned char* Read, uint8_t num)
{
    HAL_I2C_Mem_Read(&hi2c1, AP3216C_ADDRESS, reg_add, I2C_MEMADD_SIZE_8BIT, Read, num, 1000);
}

/**
 * @brief 复位AP3216C传感器
 * @param None
 * @retval None
 */
void AP3216C_Reset(void)
{
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, AP3216C_MODE_SW_RESET);
}

/**
 * @brief 设置ALS阈值
 * @param low_threshold: 低阈值
 * @param high_threshold: 高阈值
 * @retval None
 */
void AP3216C_Set_ALS_Threshold(uint16_t low_threshold, uint16_t high_threshold)
{
    AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD7_0, low_threshold & 0xFF);
    AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD15_8, (low_threshold >> 8) & 0xFF);
    AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD7_0, high_threshold & 0xFF);
    AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD15_8, (high_threshold >> 8) & 0xFF);
}

/**
 * @brief 设置PS阈值
 * @param low_threshold: 低阈值
 * @param high_threshold: 高阈值
 * @retval None
 */
void AP3216C_Set_PS_Threshold(uint16_t low_threshold, uint16_t high_threshold)
{
    AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD2_0, low_threshold & 0x07);
    AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD10_3, (low_threshold >> 3) & 0xFF);
    AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD2_0, high_threshold & 0x07);
    AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD10_3, (high_threshold >> 3) & 0xFF);
}

/**
 * @brief 获取中断状态
 * @param None
 * @retval 中断状态
 */
uint8_t AP3216C_Get_INTStatus(void)
{
    uint8_t buf;
    AP3216C_ReadData(AP3216C_INT_STATUS, &buf, 1);
    return buf;
}

/**
 * @brief 配置中断引脚
 * @param None
 * @retval None
 */
static void AP3216C_INT_Config(void)
{
    // 中断引脚已在CubeMX中配置，这里可以添加额外的中断配置
    // 例如设置中断清除方式等
    AP3216C_WriteReg(AP3216C_INT_CLEAR_MANNER, 0x01); // 手动清除中断
}

/**
 * @brief 初始化AP3216C传感器
 * @param None
 * @retval None
 */
void AP3216C_Init(void)
{
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, 0x00);        // 关闭所有功能
    AP3216C_Reset();                                    // 复位
    Delay(10);                                          // 复位后一定要延时10ms以上，否则读取数据错误
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, AP3216C_MODE_ALS_AND_PS); // 开启环境光和接近传感器
    AP3216C_Set_ALS_Threshold(10, 1000);               // 环境光阈值：10~1000，超出范围触发中断
    AP3216C_Set_PS_Threshold(200, 400);                // 接近阈值：200~400，超出范围触发中断
    AP3216C_INT_Config();                               // 配置中断
}

/**
 * @brief 读取AP3216C的环境光传感器数据
 * @param None
 * @retval ALS数据 (Lux)
 */
float AP3216C_ReadALS(void)
{
    uint8_t temp, buf[2];
    uint16_t ALS_RAW;
    float ALS_Data = 0.0;
    
    AP3216C_ReadData(AP3216C_ALS_DATA_LOW, buf, 2);
    ALS_RAW = (buf[1] << 8) | buf[0];
    
    AP3216C_ReadData(AP3216C_ALS_CONFIG, &temp, 1);
    if((temp >> 4) == AP3216C_ALS_RANGE_20661)
    {
        ALS_Data = ALS_RAW * 0.36;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_5162)
    {
        ALS_Data = ALS_RAW * 0.089;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_1291)
    {
        ALS_Data = ALS_RAW * 0.022;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_323)
    {
        ALS_Data = ALS_RAW * 0.0056;
    }
    return ALS_Data;
}

/**
 * @brief 读取AP3216C的接近传感器数据
 * @param None
 * @retval PS数据
 */
uint16_t AP3216C_ReadPS(void)
{
    uint8_t buf[2];
    uint16_t PS_Data;
    uint16_t proximity = 0;
    
    AP3216C_ReadData(AP3216C_PS_DATA_LOW, buf, 2);
    PS_Data = (buf[1] << 8) + buf[0];
    
    if(1 == ((PS_Data >> 6) & 0x01 || (PS_Data >> 14) & 0x01))
    {
        return PS_Data = 55555;  // 光线太强时接近传感器无效，返回55555
    }
    else
    {
        proximity = (PS_Data & 0x000f) + (((PS_Data >> 8) & 0x3f) << 4);
        proximity |= PS_Data & 0x8000;  // 最高位表示对象位置，0表示远离，1表示接近
        
        return proximity;
    }
}

/**
 * @brief 读取AP3216C的红外传感器数据
 * @param None
 * @retval IR数据
 */
uint16_t AP3216C_ReadIR(void)
{
    uint8_t buf[2];
    uint16_t IR_Data;
    
    AP3216C_ReadData(AP3216C_IR_DATA_LOW, buf, 2);
    IR_Data = (buf[1] << 8) | buf[0];
    IR_Data = (IR_Data & 0x0003) | ((IR_Data >> 8) & 0xFF);
    
    return IR_Data;
}

/* 测试代码 -------------------------------------------------------------------*/
#if AP3216C_ENABLE_TEST

/**
 * @brief AP3216C传感器测试函数
 * @param None
 * @retval None
 */
void AP3216C_TEST(void)
{
    float als_data;
    uint16_t ps_data, ir_data;
    char str_buffer[50];
    
    // 清屏并设置标题
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 10, 16, "AP3216C Sensor Test");
    
    // 初始化传感器
    AP3216C_Init();
    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 40, 16, "AP3216C Init: SUCCESS");
    
    // 主循环显示传感器数据
    while(1)
    {
        // 读取传感器数据
        als_data = AP3216C_ReadALS();
        ps_data = AP3216C_ReadPS();
        ir_data = AP3216C_ReadIR();
        
        // 显示环境光数据
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        sprintf(str_buffer, "ALS: %.1f Lux    ", als_data);
        LCD_ShowStringSimple(10, 80, 16, str_buffer);
        
        // 显示接近传感器数据
        if(ps_data == 55555)
        {
            LCD_ShowStringSimple(10, 110, 16, "PS: Light too strong");
        }
        else
        {
            sprintf(str_buffer, "PS: %d %s    ", ps_data & 0x7FFF, 
                   (ps_data & 0x8000) ? "(Near)" : "(Far)");
            LCD_ShowStringSimple(10, 110, 16, str_buffer);
        }
        
        // 显示红外数据
        sprintf(str_buffer, "IR: %d        ", ir_data);
        LCD_ShowStringSimple(10, 140, 16, str_buffer);
        
        // 显示环境光等级
        LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
        if(als_data < 10)
            LCD_ShowStringSimple(10, 180, 16, "Light: Very Dark  ");
        else if(als_data < 100)
            LCD_ShowStringSimple(10, 180, 16, "Light: Dark       ");
        else if(als_data < 500)
            LCD_ShowStringSimple(10, 180, 16, "Light: Normal     ");
        else if(als_data < 1000)
            LCD_ShowStringSimple(10, 180, 16, "Light: Bright     ");
        else
            LCD_ShowStringSimple(10, 180, 16, "Light: Very Bright");
        
        // 延时
        HAL_Delay(200);
    }
}

#endif /* AP3216C_ENABLE_TEST */
