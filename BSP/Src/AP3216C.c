/**
  ******************************************************************************
  * @file    AP3216C.c
  * <AUTHOR>
  * @brief   AP3216C环境光和接近传感器驱动实现
  ******************************************************************************
  * @attention
  *
  * AP3216C是一个三合一传感器，包含：
  * - 环境光传感器 (ALS)
  * - 接近传感器 (PS) 
  * - 红外LED (IR)
  *
  * I2C地址: 0x1E (7位) / 0x3C (8位)
  * 支持I2C快速模式 (400kHz)
  * 基于野火STM32F767开发板参考实现
  *
  ******************************************************************************
  */

/* 头文件包含 ------------------------------------------------------------------*/
#include <stdlib.h>
#include "AP3216C.h"

/* 测试代码需要的头文件 --------------------------------------------------------*/
#if AP3216C_ENABLE_TEST
#include "lcd.h"
#endif

/* 私有宏定义 ------------------------------------------------------------------*/
#define AP3216C_I2C_TIMEOUT         1000    // I2C超时时间(ms)
#define AP3216C_STARTUP_DELAY       20      // 启动延时(ms)
#define AP3216C_RESET_DELAY         10      // 复位延时(ms)

/* 私有变量 -------------------------------------------------------------------*/
static volatile uint8_t g_ap3216c_int_flag = 0;    // 中断标志
static AP3216C_ConfigTypeDef g_ap3216c_config;     // 传感器配置

/* 私有函数声明 ----------------------------------------------------------------*/
static AP3216C_StatusTypeDef AP3216C_WriteReg(uint8_t reg_addr, uint8_t reg_data);
static AP3216C_StatusTypeDef AP3216C_ReadData(uint8_t reg_addr, uint8_t* read_buf, uint8_t num);
static void AP3216C_Delay(uint32_t ms);
static float AP3216C_ConvertALSToLux(uint16_t als_raw);

/* 私有函数实现 ----------------------------------------------------------------*/

/**
 * @brief 延时函数
 * @param ms: 延时毫秒数
 */
static void AP3216C_Delay(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief 写数据到AP3216C寄存器
 * @param reg_addr: 寄存器地址
 * @param reg_data: 要写入的数据
 * @retval AP3216C状态
 */
static AP3216C_StatusTypeDef AP3216C_WriteReg(uint8_t reg_addr, uint8_t reg_data)
{
    uint8_t buffer[2] = {reg_addr, reg_data};
    HAL_StatusTypeDef status;
    
    status = HAL_I2C_Master_Transmit(&hi2c1, AP3216C_ADDRESS, buffer, 2, AP3216C_I2C_TIMEOUT);
    
    return (status == HAL_OK) ? AP3216C_OK : AP3216C_ERROR;
}

/**
 * @brief 从AP3216C寄存器读取数据
 * @param reg_addr: 寄存器地址
 * @param read_buf: 存储读取数据的缓冲区
 * @param num: 要读取的字节数
 * @retval AP3216C状态
 */
static AP3216C_StatusTypeDef AP3216C_ReadData(uint8_t reg_addr, uint8_t* read_buf, uint8_t num)
{
    HAL_StatusTypeDef status1, status2;
    
    if (read_buf == NULL || num == 0) {
        return AP3216C_INVALID_PARAM;
    }
    
    status1 = HAL_I2C_Master_Transmit(&hi2c1, AP3216C_ADDRESS, &reg_addr, 1, AP3216C_I2C_TIMEOUT);
    status2 = HAL_I2C_Master_Receive(&hi2c1, AP3216C_ADDRESS, read_buf, num, AP3216C_I2C_TIMEOUT);
    
    return (status1 == HAL_OK && status2 == HAL_OK) ? AP3216C_OK : AP3216C_ERROR;
}

/**
 * @brief 将ALS原始数据转换为Lux值
 * @param als_raw: ALS原始数据
 * @retval Lux值
 */
static float AP3216C_ConvertALSToLux(uint16_t als_raw)
{
    float conversion_factor;
    
    switch (g_ap3216c_config.als_range) {
        case AP3216C_ALS_RANGE_20661:
            conversion_factor = 0.36f;
            break;
        case AP3216C_ALS_RANGE_5162:
            conversion_factor = 0.089f;
            break;
        case AP3216C_ALS_RANGE_1291:
            conversion_factor = 0.022f;
            break;
        case AP3216C_ALS_RANGE_323:
            conversion_factor = 0.0056f;
            break;
        default:
            conversion_factor = 0.36f;
            break;
    }
    
    return als_raw * conversion_factor;
}

/* 公共函数实现 ----------------------------------------------------------------*/

/**
 * @brief 复位AP3216C传感器
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_Reset(void)
{
    AP3216C_StatusTypeDef status;
    
    status = AP3216C_WriteReg(AP3216C_SYS_CONFIG, AP3216C_MODE_SW_RESET);
    if (status == AP3216C_OK) {
        AP3216C_Delay(AP3216C_RESET_DELAY);
    }
    
    return status;
}

/**
 * @brief 设置AP3216C工作模式
 * @param mode: 工作模式
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_SetMode(AP3216C_ModeTypeDef mode)
{
    g_ap3216c_config.mode = mode;
    return AP3216C_WriteReg(AP3216C_SYS_CONFIG, (uint8_t)mode);
}

/**
 * @brief 设置AP3216C环境光传感器阈值
 * @param low_threshold: 低阈值 (Lux)
 * @param high_threshold: 高阈值 (Lux)
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_SetALSThreshold(uint16_t low_threshold, uint16_t high_threshold)
{
    AP3216C_StatusTypeDef status = AP3216C_OK;
    uint8_t resolution;
    float DR;
    
    // 读取环境光强度的范围
    if (AP3216C_ReadData(AP3216C_ALS_CONFIG, &resolution, 1) != AP3216C_OK) {
        return AP3216C_ERROR;
    }
    
    // 根据量程计算转换因子
    switch ((resolution >> 4) & 0x03) {
        case AP3216C_ALS_RANGE_20661:
            DR = 0.36;
            break;
        case AP3216C_ALS_RANGE_5162:
            DR = 0.089;
            break;
        case AP3216C_ALS_RANGE_1291:
            DR = 0.022;
            break;
        case AP3216C_ALS_RANGE_323:
            DR = 0.0056;
            break;
        default:
            DR = 0.36;
            break;
    }
    
    // 转换为原始数据值
    low_threshold = (uint16_t)(low_threshold / DR);
    high_threshold = (uint16_t)(high_threshold / DR);
    
    // 写入阈值寄存器
    status |= AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD7_0, (low_threshold & 0xff));
    status |= AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD15_8, low_threshold >> 8);
    status |= AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD7_0, (high_threshold & 0xff));
    status |= AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD15_8, high_threshold >> 8);
    
    if (status == AP3216C_OK) {
        g_ap3216c_config.als_low_threshold = low_threshold;
        g_ap3216c_config.als_high_threshold = high_threshold;
    }
    
    return status;
}

/**
 * @brief 设置AP3216C接近传感器阈值
 * @param low_threshold: 低阈值
 * @param high_threshold: 高阈值
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_SetPSThreshold(uint16_t low_threshold, uint16_t high_threshold)
{
    AP3216C_StatusTypeDef status = AP3216C_OK;
    
    // 设置低阈值
    if (low_threshold > 1020) {
        status |= AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD2_0, (low_threshold - 1020) & 0x03);
    }
    status |= AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD10_3, (low_threshold / 4));
    
    // 设置高阈值
    if (high_threshold > 1020) {
        status |= AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD2_0, (high_threshold - 1020) & 0x03);
    }
    status |= AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD10_3, (high_threshold / 4));
    
    if (status == AP3216C_OK) {
        g_ap3216c_config.ps_low_threshold = low_threshold;
        g_ap3216c_config.ps_high_threshold = high_threshold;
    }
    
    return status;
}

/**
 * @brief 获取AP3216C传感器中断状态
 * @retval 中断状态: bit0表示ALS中断，bit1表示PS中断
 */
uint8_t AP3216C_GetINTStatus(void)
{
    uint8_t int_status = 0;
    
    AP3216C_ReadData(AP3216C_INT_STATUS, &int_status, 1);
    
    return int_status;
}

/**
 * @brief 初始化AP3216C传感器
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_Init(void)
{
    AP3216C_StatusTypeDef status;
    
    // 初始化配置结构
    g_ap3216c_config.mode = AP3216C_MODE_ALS_AND_PS;
    g_ap3216c_config.als_range = AP3216C_ALS_RANGE_20661;
    g_ap3216c_config.als_low_threshold = 10;
    g_ap3216c_config.als_high_threshold = 1000;
    g_ap3216c_config.ps_low_threshold = 200;
    g_ap3216c_config.ps_high_threshold = 400;
    
    // 关闭所有功能
    status = AP3216C_WriteReg(AP3216C_SYS_CONFIG, 0x00);
    if (status != AP3216C_OK) return status;
    
    // 复位传感器
    status = AP3216C_Reset();
    if (status != AP3216C_OK) return status;
    
    // 启动延时
    AP3216C_Delay(AP3216C_STARTUP_DELAY);
    
    // 设置工作模式
    status = AP3216C_SetMode(g_ap3216c_config.mode);
    if (status != AP3216C_OK) return status;
    
    // 设置阈值
    status = AP3216C_SetALSThreshold(g_ap3216c_config.als_low_threshold, g_ap3216c_config.als_high_threshold);
    if (status != AP3216C_OK) return status;
    
    status = AP3216C_SetPSThreshold(g_ap3216c_config.ps_low_threshold, g_ap3216c_config.ps_high_threshold);
    
    return status;
}

/**
 * @brief 读取所有原始数据
 * @param raw_data: 原始数据结构指针
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_ReadRawData(AP3216C_RawDataTypeDef *raw_data)
{
    uint8_t buf[6];
    AP3216C_StatusTypeDef status;
    
    if (raw_data == NULL) {
        return AP3216C_INVALID_PARAM;
    }
    
    // 先读取中断状态
    raw_data->int_status = AP3216C_GetINTStatus();
    raw_data->int_pin_state = AP3216C_CheckIntPin();
    
    // 突发读取所有数据寄存器 (0x0A-0x0F)
    status = AP3216C_ReadData(AP3216C_IR_DATA_LOW, buf, 6);
    if (status != AP3216C_OK) {
        return status;
    }
    
    // 解析数据
    raw_data->ir_raw = (buf[1] << 8) | buf[0];
    raw_data->ir_raw = (raw_data->ir_raw & 0x0003) | ((raw_data->ir_raw >> 8) & 0xFF);
    
    raw_data->als_raw = (buf[3] << 8) | buf[2];
    
    uint16_t ps_temp = (buf[5] << 8) | buf[4];
    if ((ps_temp >> 6) & 0x01 || (ps_temp >> 14) & 0x01) {
        raw_data->ps_raw = 55555; // 光线太强
    } else {
        raw_data->ps_raw = (ps_temp & 0x000f) | (((ps_temp >> 8) & 0x3f) << 4);
        raw_data->ps_raw |= ps_temp & 0x8000; // 保留对象位置标志
    }
    
    return AP3216C_OK;
}

/**
 * @brief 读取AP3216C的环境光传感器数据
 * @retval ALS数据 (Lux)
 */
float AP3216C_ReadALS(void)
{
    AP3216C_RawDataTypeDef raw_data;
    
    if (AP3216C_ReadRawData(&raw_data) == AP3216C_OK) {
        return AP3216C_ConvertALSToLux(raw_data.als_raw);
    }
    
    return -1.0f; // 错误返回值
}

/**
 * @brief 读取AP3216C的接近传感器数据
 * @retval PS数据
 */
uint16_t AP3216C_ReadPS(void)
{
    AP3216C_RawDataTypeDef raw_data;
    
    if (AP3216C_ReadRawData(&raw_data) == AP3216C_OK) {
        return raw_data.ps_raw;
    }
    
    return 0; // 错误返回值
}

/**
 * @brief 读取AP3216C的红外传感器数据
 * @retval IR数据
 */
uint16_t AP3216C_ReadIR(void)
{
    AP3216C_RawDataTypeDef raw_data;
    
    if (AP3216C_ReadRawData(&raw_data) == AP3216C_OK) {
        return raw_data.ir_raw;
    }
    
    return 0; // 错误返回值
}

/**
 * @brief PE3外部中断处理函数
 * @note 在stm32f7xx_it.c的HAL_GPIO_EXTI_Callback中调用
 * @note 不在中断中进行阻塞操作，只设置标志位
 */
void AP3216C_EXTI3_IRQHandler(void)
{
    g_ap3216c_int_flag = 1;
}

/**
 * @brief 处理AP3216C中断（在主循环中调用）
 * @retval 是否有中断发生 (1:有中断, 0:无中断)
 */
uint8_t AP3216C_ProcessInterrupt(void)
{
    if (g_ap3216c_int_flag) {
        g_ap3216c_int_flag = 0;
        return 1;
    }
    return 0;
}

/**
 * @brief 检查中断引脚状态
 * @retval 引脚状态 (0:低电平, 1:高电平)
 */
uint8_t AP3216C_CheckIntPin(void)
{
    return AP_INT_Read();
}

/* 数据处理函数 ----------------------------------------------------------------*/

/**
 * @brief 获取环境光等级描述
 * @param lux_value: 环境光值(Lux)
 * @retval 环境光等级字符串
 */
const char* AP3216C_GetLightLevelString(float lux_value)
{
    if (lux_value > 10000) {
        return "Very Bright";
    } else if (lux_value > 1000) {
        return "Bright     ";
    } else if (lux_value > 500) {
        return "Normal     ";
    } else if (lux_value > 100) {
        return "Dim        ";
    } else if (lux_value > 10) {
        return "Dark       ";
    } else {
        return "Very Dark  ";
    }
}

/**
 * @brief 获取接近状态描述
 * @param ps_data: 接近传感器数据
 * @retval 接近状态字符串
 */
const char* AP3216C_GetProximityString(uint16_t ps_data)
{
    if (ps_data == 55555) {
        return "Light Too Strong";
    } else if (ps_data & 0x8000) {
        return "Object Close    ";
    } else {
        return "Object Far      ";
    }
}

/**
 * @brief 计算接近距离估算值
 * @param ps_data: 接近传感器数据
 * @retval 估算距离(cm)，-1表示无效
 */
float AP3216C_CalculateDistance(uint16_t ps_data)
{
    if (ps_data == 55555) {
        return -1.0f;
    }

    uint16_t proximity_value = ps_data & 0x7FFF;

    if (proximity_value == 0) {
        return 10.0f;
    }

    float distance = 1000.0f / (float)(proximity_value + 50);
    if (distance > 10.0f) {
        distance = 10.0f;
    }

    return distance;
}

/**
 * @brief 计算红外强度百分比
 * @param ir_data: 红外数据
 * @retval 红外强度百分比(0-100%)
 */
float AP3216C_CalculateIRIntensity(uint16_t ir_data)
{
    return ((float)ir_data / 1023.0f) * 100.0f;
}

/**
 * @brief 处理所有传感器数据并返回处理结果
 * @param processed_data: 处理后数据结构指针
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_ProcessAllData(AP3216C_ProcessedDataTypeDef *processed_data)
{
    AP3216C_RawDataTypeDef raw_data;
    AP3216C_StatusTypeDef status;
    
    if (processed_data == NULL) {
        return AP3216C_INVALID_PARAM;
    }

    // 读取原始数据
    status = AP3216C_ReadRawData(&raw_data);
    if (status != AP3216C_OK) {
        return status;
    }

    // 转换和处理数据
    processed_data->als_lux = AP3216C_ConvertALSToLux(raw_data.als_raw);
    processed_data->ps_raw = raw_data.ps_raw;
    processed_data->ir_raw = raw_data.ir_raw;
    processed_data->int_status = raw_data.int_status;
    processed_data->int_pin_state = raw_data.int_pin_state;

    // 生成描述性信息
    processed_data->light_level = AP3216C_GetLightLevelString(processed_data->als_lux);
    processed_data->proximity_status = AP3216C_GetProximityString(processed_data->ps_raw);
    processed_data->distance_cm = AP3216C_CalculateDistance(processed_data->ps_raw);
    processed_data->ir_intensity = AP3216C_CalculateIRIntensity(processed_data->ir_raw);

    return AP3216C_OK;
}

/* 高级数据处理函数 ------------------------------------------------------------*/

/**
 * @brief 获取环境等级数值
 * @param lux_value: 环境光值(Lux)
 * @param level: 等级输出 (0-5)
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_GetEnvironmentLevel(float lux_value, uint8_t *level)
{
    if (level == NULL) {
        return AP3216C_INVALID_PARAM;
    }
    
    if (lux_value > 10000) {
        *level = 5; // Very Bright
    } else if (lux_value > 1000) {
        *level = 4; // Bright
    } else if (lux_value > 500) {
        *level = 3; // Normal
    } else if (lux_value > 100) {
        *level = 2; // Dim
    } else if (lux_value > 10) {
        *level = 1; // Dark
    } else {
        *level = 0; // Very Dark
    }
    
    return AP3216C_OK;
}

/**
 * @brief 检测运动
 * @param ps_data: 接近传感器数据
 * @param motion_detected: 运动检测结果
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_DetectMotion(uint16_t ps_data, uint8_t *motion_detected)
{
    static uint16_t last_ps_data = 0;
    static uint8_t motion_threshold = 50;
    
    if (motion_detected == NULL) {
        return AP3216C_INVALID_PARAM;
    }
    
    if (ps_data == 55555) {
        *motion_detected = 0;
        return AP3216C_OK;
    }
    
    uint16_t current_value = ps_data & 0x7FFF;
    uint16_t last_value = last_ps_data & 0x7FFF;
    
    if (abs((int)current_value - (int)last_value) > motion_threshold) {
        *motion_detected = 1;
    } else {
        *motion_detected = 0;
    }
    
    last_ps_data = ps_data;
    return AP3216C_OK;
}

/**
 * @brief 计算平均数据
 * @param avg_data: 平均数据输出
 * @param samples: 采样次数
 * @retval AP3216C状态
 */
AP3216C_StatusTypeDef AP3216C_CalculateAverageData(AP3216C_ProcessedDataTypeDef *avg_data, uint8_t samples)
{
    if (avg_data == NULL || samples == 0) {
        return AP3216C_INVALID_PARAM;
    }
    
    float sum_als = 0;
    uint32_t sum_ps = 0, sum_ir = 0;
    uint8_t valid_samples = 0;
    
    for (uint8_t i = 0; i < samples; i++) {
        AP3216C_ProcessedDataTypeDef temp_data;
        if (AP3216C_ProcessAllData(&temp_data) == AP3216C_OK) {
            sum_als += temp_data.als_lux;
            sum_ps += (temp_data.ps_raw & 0x7FFF);
            sum_ir += temp_data.ir_raw;
            valid_samples++;
        }
        AP3216C_Delay(10); // 采样间隔
    }
    
    if (valid_samples == 0) {
        return AP3216C_ERROR;
    }
    
    // 计算平均值
    avg_data->als_lux = sum_als / valid_samples;
    avg_data->ps_raw = sum_ps / valid_samples;
    avg_data->ir_raw = sum_ir / valid_samples;
    
    // 生成描述信息
    avg_data->light_level = AP3216C_GetLightLevelString(avg_data->als_lux);
    avg_data->proximity_status = AP3216C_GetProximityString(avg_data->ps_raw);
    avg_data->distance_cm = AP3216C_CalculateDistance(avg_data->ps_raw);
    avg_data->ir_intensity = AP3216C_CalculateIRIntensity(avg_data->ir_raw);
    
    return AP3216C_OK;
}

/* 测试代码 -------------------------------------------------------------------*/
#if AP3216C_ENABLE_TEST

/**
 * @brief AP3216C传感器测试函数
 * @note 使用LCD显示测试结果
 */
void AP3216C_TEST(void)
{
    char display_buffer[100];
    uint32_t test_count = 0;
    uint32_t last_update = 0;
    
    // 清屏并显示测试标题
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 10, 24, "AP3216C Sensor Test");
    LCD_ShowStringSimple(10, 40, 16, "STM32F767 + AP3216C I2C Test");
    
    // 初始化传感器
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 70, 16, "Initializing AP3216C...");
    
    AP3216C_StatusTypeDef init_status = AP3216C_Init();
    if (init_status == AP3216C_OK) {
        LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 90, 16, "AP3216C Init: SUCCESS");
    } else {
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        LCD_ShowStringSimple(10, 90, 16, "AP3216C Init: FAILED");
        return;
    }
    
    // 显示测试界面布局
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 120, 14, "Real-time Sensor Data:");
    LCD_ShowStringSimple(10, 140, 12, "----------------------------------------");
    
    // 标签
    LCD_ShowStringSimple(10, 160, 12, "ALS (Lux):");
    LCD_ShowStringSimple(10, 180, 12, "PS (Raw):");
    LCD_ShowStringSimple(10, 200, 12, "IR (Raw):");
    LCD_ShowStringSimple(10, 220, 12, "Distance:");
    LCD_ShowStringSimple(10, 240, 12, "Light Level:");
    LCD_ShowStringSimple(10, 260, 12, "Proximity:");
    LCD_ShowStringSimple(10, 280, 12, "IR Intensity:");
    LCD_ShowStringSimple(10, 300, 12, "Interrupt:");
    LCD_ShowStringSimple(10, 320, 12, "Test Count:");
    
    // 右侧显示区域
    LCD_ShowStringSimple(400, 160, 12, "Status:");
    LCD_ShowStringSimple(400, 180, 12, "Motion:");
    LCD_ShowStringSimple(400, 200, 12, "Env Level:");
    LCD_ShowStringSimple(400, 220, 12, "Pin State:");
    
    // 测试循环
    while (1) {
        // 处理中断
        if (AP3216C_ProcessInterrupt()) {
            LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
            LCD_ShowStringSimple(400, 240, 12, "*** INTERRUPT ***");
        }
        
        // 每200ms更新一次数据
        if (HAL_GetTick() - last_update >= 200) {
            last_update = HAL_GetTick();
            test_count++;
            
            // 读取处理后的数据
            AP3216C_ProcessedDataTypeDef sensor_data;
            AP3216C_StatusTypeDef status = AP3216C_ProcessAllData(&sensor_data);
            
            if (status == AP3216C_OK) {
                // 显示传感器数据
                LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
                
                sprintf(display_buffer, "%.1f        ", sensor_data.als_lux);
                LCD_ShowStringSimple(120, 160, 12, display_buffer);
                
                sprintf(display_buffer, "%d        ", sensor_data.ps_raw & 0x7FFF);
                LCD_ShowStringSimple(120, 180, 12, display_buffer);
                
                sprintf(display_buffer, "%d        ", sensor_data.ir_raw);
                LCD_ShowStringSimple(120, 200, 12, display_buffer);
                
                if (sensor_data.distance_cm >= 0) {
                    sprintf(display_buffer, "%.1f cm     ", sensor_data.distance_cm);
                } else {
                    sprintf(display_buffer, "Invalid     ");
                }
                LCD_ShowStringSimple(120, 220, 12, display_buffer);
                
                sprintf(display_buffer, "%s", sensor_data.light_level);
                LCD_ShowStringSimple(120, 240, 12, display_buffer);
                
                sprintf(display_buffer, "%s", sensor_data.proximity_status);
                LCD_ShowStringSimple(120, 260, 12, display_buffer);
                
                sprintf(display_buffer, "%.1f%%      ", sensor_data.ir_intensity);
                LCD_ShowStringSimple(120, 280, 12, display_buffer);
                
                sprintf(display_buffer, "0x%02X      ", sensor_data.int_status);
                LCD_ShowStringSimple(120, 300, 12, display_buffer);
                
                sprintf(display_buffer, "%lu        ", test_count);
                LCD_ShowStringSimple(120, 320, 12, display_buffer);
                
                // 右侧状态信息
                LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(480, 160, 12, "OK    ");
                
                // 运动检测
                uint8_t motion_detected;
                AP3216C_DetectMotion(sensor_data.ps_raw, &motion_detected);
                if (motion_detected) {
                    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(480, 180, 12, "YES   ");
                } else {
                    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(480, 180, 12, "NO    ");
                }
                
                // 环境等级
                uint8_t env_level;
                AP3216C_GetEnvironmentLevel(sensor_data.als_lux, &env_level);
                LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
                sprintf(display_buffer, "%d     ", env_level);
                LCD_ShowStringSimple(480, 200, 12, display_buffer);
                
                // 引脚状态
                if (sensor_data.int_pin_state) {
                    LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(480, 220, 12, "HIGH  ");
                } else {
                    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(480, 220, 12, "LOW   ");
                }
                
                // 清除中断提示
                if (sensor_data.int_status == 0) {
                    LCD_SetColor(LCD_COLOR_WHITE, LCD_COLOR_WHITE);
                    LCD_ShowStringSimple(400, 240, 12, "                 ");
                }
                
            } else {
                // 显示错误
                LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(480, 160, 12, "ERROR ");
            }
            
            // 显示运行时间
            LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
            sprintf(display_buffer, "Runtime: %lu s", HAL_GetTick() / 1000);
            LCD_ShowStringSimple(10, 360, 12, display_buffer);
            
            // 显示测试说明
            if (test_count % 50 == 0) { // 每10秒显示一次说明
                LCD_SetColor(LCD_COLOR_DARK_GRAY, LCD_COLOR_WHITE);
                LCD_ShowStringSimple(10, 400, 10, "Test: Cover sensor to trigger proximity");
                LCD_ShowStringSimple(10, 415, 10, "Change light to see ALS values");
                LCD_ShowStringSimple(10, 430, 10, "Motion detection based on PS changes");
            }
        }
        
        // 简单的延时，避免过度占用CPU
        HAL_Delay(10);
    }
}

#endif /* AP3216C_ENABLE_TEST */