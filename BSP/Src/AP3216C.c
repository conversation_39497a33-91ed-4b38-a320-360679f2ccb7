/**
  ******************************************************************************
  * @file    AP3216C.c
  * <AUTHOR>
  * @brief   AP3216C环境光和接近传感器驱动实现 (简化版)
  ******************************************************************************
  * @attention
  *
  * AP3216C是一个三合一传感器，包含：
  * - 环境光传感器 (ALS)
  * - 接近传感器 (PS) 
  * - 红外LED (IR)
  *
  * I2C地址: 0x1E (7位) / 0x3C (8位)
  * 支持I2C快速模式 (400kHz)
  * 参考野火STM32F767开发板实现，简化代码结构
  *
  ******************************************************************************
  */

#include "AP3216C.h"
#include "lcd.h"

/* 私有函数声明 ----------------------------------------------------------------*/
static void AP3216C_WriteReg(uint8_t reg_add, uint8_t reg_dat);
static void AP3216C_ReadData(uint8_t reg_add, unsigned char* Read, uint8_t num);
static void AP3216C_INT_Config(void);

/**
 * @brief 写数据到AP3216C寄存器
 * @param reg_add: 寄存器地址
 * @param reg_data: 要写入的数据
 * @retval None
 */
static void AP3216C_WriteReg(uint8_t reg_add, uint8_t reg_dat)
{
    HAL_I2C_Mem_Write(&hi2c1, AP3216C_ADDRESS, reg_add, I2C_MEMADD_SIZE_8BIT, &reg_dat, 1, 1000);
}

/**
 * @brief 从AP3216C寄存器读取数据
 * @param reg_add: 寄存器地址
 * @param Read: 存储数据的缓冲区
 * @param num: 要读取的字节数
 * @retval None
 */
static void AP3216C_ReadData(uint8_t reg_add, unsigned char* Read, uint8_t num)
{
    HAL_I2C_Mem_Read(&hi2c1, AP3216C_ADDRESS, reg_add, I2C_MEMADD_SIZE_8BIT, Read, num, 1000);
}

/**
 * @brief 复位AP3216C传感器
 * @param None
 * @retval None
 */
void AP3216C_Reset(void)
{
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, AP3216C_MODE_SW_RESET);
}

/**
 * @brief 设置ALS阈值 (参考野火实现)
 * @param low_threshold: 低阈值 (Lux)
 * @param high_threshold: 高阈值 (Lux)
 * @retval None
 */
void AP3216C_Set_ALS_Threshold(uint16_t low_threshold, uint16_t high_threshold)
{
    uint8_t resolution;
    double DR;

    /* 获取光强度的范围 */
    AP3216C_ReadData(AP3216C_ALS_CONFIG, &resolution, 1);
    if((resolution >> 4) == AP3216C_ALS_RANGE_20661)
    {
        DR = 0.36;
    }
    else if((resolution >> 4) == AP3216C_ALS_RANGE_5162)
    {
        DR = 0.089;
    }
    else if((resolution >> 4) == AP3216C_ALS_RANGE_1291)
    {
        DR = 0.022;
    }
    else if((resolution >> 4) == AP3216C_ALS_RANGE_323)
    {
        DR = 0.0056;
    }

    low_threshold = (uint16_t)(low_threshold / DR);
    high_threshold = (uint16_t)(high_threshold / DR);

    AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD7_0, (low_threshold & 0xff));
    AP3216C_WriteReg(AP3216C_ALS_LOW_THRESHOLD15_8, low_threshold >> 8);
    AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD7_0, (high_threshold & 0xff));
    AP3216C_WriteReg(AP3216C_ALS_HIGH_THRESHOLD15_8, high_threshold >> 8);
}

/**
 * @brief 设置PS阈值 (参考野火实现)
 * @param low_threshold: 低阈值
 * @param high_threshold: 高阈值
 * @retval None
 */
void AP3216C_Set_PS_Threshold(uint16_t low_threshold, uint16_t high_threshold)
{
    if(low_threshold > 1020)
    {
        AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD2_0, (low_threshold - 1020 & 0x03));
    }
    AP3216C_WriteReg(AP3216C_PS_LOW_THRESHOLD10_3, (low_threshold / 4));

    if(high_threshold > 1020)
    {
        AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD2_0, (high_threshold - 1020 & 0x03));
    }
    AP3216C_WriteReg(AP3216C_PS_HIGH_THRESHOLD10_3, (high_threshold / 4));
}

/**
 * @brief 获取中断状态
 * @param None
 * @retval 中断状态
 */
uint8_t AP3216C_Get_INTStatus(void)
{
    uint8_t buf;
    AP3216C_ReadData(AP3216C_INT_STATUS, &buf, 1);
    return buf;
}

/**
 * @brief 配置中断引脚
 * @param None
 * @retval None
 */
static void AP3216C_INT_Config(void)
{
    // 中断引脚已在CubeMX中配置，这里可以添加额外的中断配置
    // 例如设置中断清除方式等
    AP3216C_WriteReg(AP3216C_INT_CLEAR_MANNER, 0x01); // 手动清除中断
}

/**
 * @brief 初始化AP3216C传感器
 * @param None
 * @retval None
 */
void AP3216C_Init(void)
{
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, 0x00);        // 关闭所有功能
    AP3216C_Reset();                                    // 复位
    Delay(10);                                          // 复位后一定要延时10ms以上，否则读取数据错误
    AP3216C_WriteReg(AP3216C_SYS_CONFIG, AP3216C_MODE_ALS_AND_PS); // 开启环境光和接近传感器
    AP3216C_Set_ALS_Threshold(10, 1000);               // 环境光阈值：10~1000，超出范围触发中断
    AP3216C_Set_PS_Threshold(200, 400);                // 接近阈值：200~400，超出范围触发中断
    AP3216C_INT_Config();                               // 配置中断
}

/* 原始数据读取函数 ----------------------------------------------------------------*/

/**
 * @brief 读取AP3216C的环境光传感器原始数据
 * @param None
 * @retval ALS原始数据
 */
uint16_t AP3216C_ReadALS_Raw(void)
{
    uint8_t buf[2];
    uint16_t ALS_RAW;

    AP3216C_ReadData(AP3216C_ALS_DATA_LOW, buf, 2);
    ALS_RAW = (buf[1] << 8) | buf[0];

    return ALS_RAW;
}

/**
 * @brief 读取AP3216C的接近传感器原始数据
 * @param None
 * @retval PS原始数据
 */
uint16_t AP3216C_ReadPS_Raw(void)
{
    uint8_t buf[2];
    uint16_t PS_Data;
    uint16_t proximity = 0;

    AP3216C_ReadData(AP3216C_PS_DATA_LOW, buf, 2);
    PS_Data = (buf[1] << 8) + buf[0];

    if(1 == ((PS_Data >> 6) & 0x01 || (PS_Data >> 12) & 0x01))
    {
        return 55555;  // 光线太强时接近传感器无效，返回55555
    }
    else
    {
        proximity = (PS_Data & 0x000f) + (((PS_Data >> 8) & 0x3f) << 4);
        proximity |= PS_Data & 0x8000;  // 最高位表示对象位置，0表示远离，1表示接近

        return proximity;
    }
}

/**
 * @brief 读取AP3216C的红外传感器原始数据
 * @param None
 * @retval IR原始数据
 */
uint16_t AP3216C_ReadIR_Raw(void)
{
    uint8_t buf[2];
    uint16_t IR_Data;

    AP3216C_ReadData(AP3216C_IR_DATA_LOW, buf, 2);
    IR_Data = (buf[1] << 8) + buf[0];
    IR_Data = (IR_Data & 0x0003) + ((IR_Data >> 8) & 0xFF);

    return IR_Data;
}

/* 转换后数据读取函数 ----------------------------------------------------------------*/

/**
 * @brief 读取AP3216C的环境光传感器数据 (转换为Lux)
 * @param None
 * @retval ALS数据 (Lux)
 */
float AP3216C_ReadALS(void)
{
    uint8_t temp;
    uint16_t ALS_RAW;
    float ALS_Data = 0.0;

    ALS_RAW = AP3216C_ReadALS_Raw();

    AP3216C_ReadData(AP3216C_ALS_CONFIG, &temp, 1);
    if((temp >> 4) == AP3216C_ALS_RANGE_20661)
    {
        ALS_Data = ALS_RAW * 0.36f;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_5162)
    {
        ALS_Data = ALS_RAW * 0.089f;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_1291)
    {
        ALS_Data = ALS_RAW * 0.022f;
    }
    else if((temp >> 4) == AP3216C_ALS_RANGE_323)
    {
        ALS_Data = ALS_RAW * 0.0056f;
    }
    return ALS_Data;
}

/**
 * @brief 读取AP3216C的接近传感器数据并转换为距离
 * @param None
 * @retval 距离 (cm)，-1表示光线太强无法测量
 */
float AP3216C_ReadPS_Distance(void)
{
    uint16_t ps_raw = AP3216C_ReadPS_Raw();

    if (ps_raw == 55555) {
        return -1.0f;  // 光线太强，无法测量
    }

    uint16_t ps_value = ps_raw & 0x7FFF;  // 去除最高位状态位

    // 根据经验公式将PS值转换为距离 (cm)
    // PS值越大表示越近，这里使用反比例关系
    if (ps_value == 0) {
        return 100.0f;  // 最远距离
    }

    // 经验公式：距离 = 1000 / (PS值 + 10)，范围约0.5-10cm
    float distance = 1000.0f / (ps_value + 10.0f);

    // 限制距离范围
    if (distance > 50.0f) distance = 50.0f;
    if (distance < 0.5f) distance = 0.5f;

    return distance;
}

/**
 * @brief 读取AP3216C的红外传感器数据并转换为强度百分比
 * @param None
 * @retval 红外强度 (%)
 */
float AP3216C_ReadIR_Intensity(void)
{
    uint16_t ir_raw = AP3216C_ReadIR_Raw();

    // 将IR原始数据转换为百分比 (0-100%)
    // IR最大值约为1023
    float intensity = (ir_raw / 1023.0f) * 100.0f;

    // 限制范围
    if (intensity > 100.0f) intensity = 100.0f;
    if (intensity < 0.0f) intensity = 0.0f;

    return intensity;
}

/* 兼容函数 ----------------------------------------------------------------*/

/**
 * @brief 兼容函数：读取PS原始数据
 * @param None
 * @retval PS原始数据
 */
uint16_t AP3216C_ReadPS(void)
{
    return AP3216C_ReadPS_Raw();
}

/**
 * @brief 兼容函数：读取IR原始数据
 * @param None
 * @retval IR原始数据
 */
uint16_t AP3216C_ReadIR(void)
{
    return AP3216C_ReadIR_Raw();
}

/* 测试代码 -------------------------------------------------------------------*/
#if AP3216C_ENABLE_TEST

/**
 * @brief AP3216C传感器测试函数 (增强版)
 * @param None
 * @retval None
 */
void AP3216C_TEST(void)
{
    float als_lux, ps_distance, ir_intensity;
    uint16_t als_raw, ps_raw, ir_raw;
    char str_buffer[60];

    // 清屏并设置标题
    LCD_Clear(LCD_COLOR_WHITE);
    LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 10, 16, "AP3216C Enhanced Test");

    // 初始化传感器
    AP3216C_Init();
    LCD_SetColor(LCD_COLOR_GREEN, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 40, 16, "AP3216C Init: SUCCESS");

    // 显示说明
    LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
    LCD_ShowStringSimple(10, 60, 12, "Raw | Converted Data");

    // 主循环显示传感器数据
    while(1)
    {
        // 读取原始数据
        als_raw = AP3216C_ReadALS_Raw();
        ps_raw = AP3216C_ReadPS_Raw();
        ir_raw = AP3216C_ReadIR_Raw();

        // 读取转换后数据
        als_lux = AP3216C_ReadALS();
        ps_distance = AP3216C_ReadPS_Distance();
        ir_intensity = AP3216C_ReadIR_Intensity();

        // 显示环境光数据
        LCD_SetColor(LCD_COLOR_BLACK, LCD_COLOR_WHITE);
        sprintf(str_buffer, "ALS: %d | %.1f Lux     ", als_raw, als_lux);
        LCD_ShowStringSimple(10, 90, 12, str_buffer);

        // 显示接近传感器数据
        if(ps_raw == 55555)
        {
            LCD_ShowStringSimple(10, 110, 12, "PS: Light too strong      ");
        }
        else
        {
            if(ps_distance >= 0)
            {
                sprintf(str_buffer, "PS: %d | %.1fcm %s   ", ps_raw & 0x7FFF, ps_distance,
                       (ps_raw & 0x8000) ? "Near" : "Far");
            }
            else
            {
                sprintf(str_buffer, "PS: %d | Error        ", ps_raw & 0x7FFF);
            }
            LCD_ShowStringSimple(10, 110, 12, str_buffer);
        }

        // 显示红外数据
        sprintf(str_buffer, "IR: %d | %.1f%%        ", ir_raw, ir_intensity);
        LCD_ShowStringSimple(10, 130, 12, str_buffer);

        // 显示环境光等级
        LCD_SetColor(LCD_COLOR_BLUE, LCD_COLOR_WHITE);
        if(als_lux < 10)
            LCD_ShowStringSimple(10, 160, 12, "Light: Very Dark     ");
        else if(als_lux < 100)
            LCD_ShowStringSimple(10, 160, 12, "Light: Dark          ");
        else if(als_lux < 500)
            LCD_ShowStringSimple(10, 160, 12, "Light: Normal        ");
        else if(als_lux < 1000)
            LCD_ShowStringSimple(10, 160, 12, "Light: Bright        ");
        else
            LCD_ShowStringSimple(10, 160, 12, "Light: Very Bright   ");

        // 显示距离状态
        LCD_SetColor(LCD_COLOR_RED, LCD_COLOR_WHITE);
        if(ps_distance >= 0)
        {
            if(ps_distance < 2.0f)
                LCD_ShowStringSimple(10, 180, 12, "Distance: Very Close ");
            else if(ps_distance < 5.0f)
                LCD_ShowStringSimple(10, 180, 12, "Distance: Close      ");
            else if(ps_distance < 10.0f)
                LCD_ShowStringSimple(10, 180, 12, "Distance: Medium     ");
            else
                LCD_ShowStringSimple(10, 180, 12, "Distance: Far        ");
        }
        else
        {
            LCD_ShowStringSimple(10, 180, 12, "Distance: Invalid    ");
        }

        // 显示中断状态
        uint8_t int_status = AP3216C_Get_INTStatus();
        LCD_SetColor(LCD_COLOR_MAGENTA, LCD_COLOR_WHITE);
        sprintf(str_buffer, "INT: 0x%02X            ", int_status);
        LCD_ShowStringSimple(10, 200, 12, str_buffer);

        // 延时
        HAL_Delay(300);
    }
}

#endif /* AP3216C_ENABLE_TEST */
