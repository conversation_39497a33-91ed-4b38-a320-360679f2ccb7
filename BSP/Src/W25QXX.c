#include "W25QXX.h"

/* 私有函数声明 */
#if USE_QSPI_MODE
static HAL_StatusTypeDef W25QXX_QSPI_Command(QSPI_CommandTypeDef* cmd);
static HAL_StatusTypeDef W25QXX_QSPI_Transmit(uint8_t* pData, uint32_t timeout);
static HAL_StatusTypeDef W25QXX_QSPI_Receive(uint8_t* pData, uint32_t timeout);
#elif USE_SPI_MODE
static HAL_StatusTypeDef W25QXX_SPI_Transmit(uint8_t* pData, uint16_t size);
static HAL_StatusTypeDef W25QXX_SPI_Receive(uint8_t* pData, uint16_t size);
static HAL_StatusTypeDef W25QXX_SPI_TransmitReceive(uint8_t* pTxData, uint8_t* pRxData, uint16_t size);
#endif

/**
 * @brief  初始化W25QXX
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_Init(void)
{
    uint16_t id;
    
#if USE_SPI_MODE
    W25QXX_CS_HIGH();
    HAL_Delay(10);
#endif

    /* 唤醒芯片 */
    W25QXX_WakeUp();
    HAL_Delay(1);
    
#if W25QXX_4BYTE_ADDR_MODE
    /* 进入四字节地址模式 */
#if USE_QSPI_MODE
    QSPI_CommandTypeDef addr_cmd;
    
    addr_cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    addr_cmd.Instruction = W25X_Enter4ByteMode;
    addr_cmd.AddressMode = QSPI_ADDRESS_NONE;
    addr_cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    addr_cmd.DataMode = QSPI_DATA_NONE;
    addr_cmd.DummyCycles = 0;
    addr_cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    addr_cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    addr_cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    if(W25QXX_QSPI_Command(&addr_cmd) != HAL_OK)
        return HAL_ERROR;
#elif USE_SPI_MODE
    uint8_t cmd = W25X_Enter4ByteMode;
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    if(status != HAL_OK)
        return HAL_ERROR;
#endif
    HAL_Delay(1);
#endif

    /* 读取芯片ID验证 */
    id = W25QXX_ReadID();
    if(id != W25QXX_CHIP_ID)
        return HAL_ERROR;
    
    return HAL_OK;
}

/**
 * @brief  读取芯片ID
 * @retval 芯片ID
 */
uint16_t W25QXX_ReadID(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    uint8_t data[2];
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_ManufactDeviceID;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = 0x000000;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = 2;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return 0;
    
    if(W25QXX_QSPI_Receive(data, W25QXX_TIMEOUT) != HAL_OK)
        return 0;
    
    return (data[0] << 8) | data[1];
    
#elif USE_SPI_MODE
    uint8_t cmd[4] = {W25X_ManufactDeviceID, 0x00, 0x00, 0x00};
    uint8_t data[2];
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 4);
    if(status == HAL_OK)
    {
        status = W25QXX_SPI_Receive(data, 2);
    }
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return 0;
    
    return (data[0] << 8) | data[1];
#endif
}

/**
 * @brief  读取JEDEC ID
 * @retval JEDEC ID
 */
uint32_t W25QXX_ReadJedecID(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    uint8_t data[3];
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_JedecDeviceID;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = 3;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return 0;
    
    if(W25QXX_QSPI_Receive(data, W25QXX_TIMEOUT) != HAL_OK)
        return 0;
    
    return (data[0] << 16) | (data[1] << 8) | data[2];
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_JedecDeviceID;
    uint8_t data[3];
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    if(status == HAL_OK)
    {
        status = W25QXX_SPI_Receive(data, 3);
    }
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return 0;
    
    return (data[0] << 16) | (data[1] << 8) | data[2];
#endif
}

/**
 * @brief  写使能
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WriteEnable(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_WriteEnable;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    return W25QXX_QSPI_Command(&cmd);
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_WriteEnable;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    
    return status;
#endif
}

/**
 * @brief  写禁止
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WriteDisable(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_WriteDisable;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    return W25QXX_QSPI_Command(&cmd);
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_WriteDisable;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    
    return status;
#endif
}

/**
 * @brief  读取状态寄存器1
 * @retval 状态寄存器值
 */
uint8_t W25QXX_ReadStatusReg1(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    uint8_t status;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_ReadStatusReg1;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = 1;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return 0xFF;
    
    if(W25QXX_QSPI_Receive(&status, W25QXX_TIMEOUT) != HAL_OK)
        return 0xFF;
    
    return status;
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_ReadStatusReg1;
    uint8_t status;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef result = W25QXX_SPI_Transmit(&cmd, 1);
    if(result == HAL_OK)
    {
        result = W25QXX_SPI_Receive(&status, 1);
    }
    W25QXX_CS_HIGH();
    
    if(result != HAL_OK)
        return 0xFF;
    
    return status;
#endif
}

/**
 * @brief  等待写操作完成
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WaitForWriteEnd(void)
{
    uint32_t timeout = HAL_GetTick() + W25QXX_TIMEOUT;
    
    while((W25QXX_ReadStatusReg1() & 0x01) == 0x01)
    {
        if(HAL_GetTick() > timeout)
            return HAL_TIMEOUT;
        HAL_Delay(1);
    }
    
    return HAL_OK;
}

/**
 * @brief  进入掉电模式
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_PowerDown(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_PowerDown;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    return W25QXX_QSPI_Command(&cmd);
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_PowerDown;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    
    return status;
#endif
}

/**
 * @brief  唤醒芯片
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WakeUp(void)
{
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_ReleasePowerDown;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    return W25QXX_QSPI_Command(&cmd);
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_ReleasePowerDown;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    
    return status;
#endif
}

/**
 * @brief  读取数据
 * @param  addr: 读取地址
 * @param  pBuffer: 数据缓冲区
 * @param  numByteToRead: 读取字节数
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_ReadData(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToRead)
{
#if USE_SPI_MODE
    uint8_t cmd[1 + W25QXX_ADDR_BYTES];
    cmd[0] = W25X_ReadData;
    
#if W25QXX_4BYTE_ADDR_MODE
    cmd[1] = (addr >> 24) & 0xFF;
    cmd[2] = (addr >> 16) & 0xFF;
    cmd[3] = (addr >> 8) & 0xFF;
    cmd[4] = addr & 0xFF;
#else
    cmd[1] = (addr >> 16) & 0xFF;
    cmd[2] = (addr >> 8) & 0xFF;
    cmd[3] = addr & 0xFF;
#endif
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 1 + W25QXX_ADDR_BYTES);
    if(status == HAL_OK)
    {
        status = W25QXX_SPI_Receive(pBuffer, numByteToRead);
    }
    W25QXX_CS_HIGH();
    
    return status;
    
#elif USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_ReadData;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = addr;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = numByteToRead;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
    
    return W25QXX_QSPI_Receive(pBuffer, W25QXX_TIMEOUT);
#endif
}

/**
 * @brief  快速读取数据
 * @param  addr: 读取地址
 * @param  pBuffer: 数据缓冲区
 * @param  numByteToRead: 读取字节数
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_FastRead(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToRead)
{
#if USE_SPI_MODE
    uint8_t cmd[1 + W25QXX_ADDR_BYTES + 1];
    cmd[0] = W25X_FastReadData;
    
#if W25QXX_4BYTE_ADDR_MODE
    cmd[1] = (addr >> 24) & 0xFF;
    cmd[2] = (addr >> 16) & 0xFF;
    cmd[3] = (addr >> 8) & 0xFF;
    cmd[4] = addr & 0xFF;
    cmd[5] = 0xFF;  // dummy byte
#else
    cmd[1] = (addr >> 16) & 0xFF;
    cmd[2] = (addr >> 8) & 0xFF;
    cmd[3] = addr & 0xFF;
    cmd[4] = 0xFF;  // dummy byte
#endif
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 1 + W25QXX_ADDR_BYTES + 1);
    if(status == HAL_OK)
    {
        status = W25QXX_SPI_Receive(pBuffer, numByteToRead);
    }
    W25QXX_CS_HIGH();
    
    return status;
    
#elif USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_FastReadData;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = addr;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 8;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = numByteToRead;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
    
    return W25QXX_QSPI_Receive(pBuffer, W25QXX_TIMEOUT);
#endif
}

/**
 * @brief  页编程
 * @param  addr: 写入地址
 * @param  pBuffer: 数据缓冲区
 * @param  numByteToWrite: 写入字节数
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WritePage(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToWrite)
{
    if(numByteToWrite > W25QXX_PAGE_SIZE)
        return HAL_ERROR;
    
    if(W25QXX_WriteEnable() != HAL_OK)
        return HAL_ERROR;
    
#if USE_SPI_MODE
    uint8_t cmd[1 + W25QXX_ADDR_BYTES];
    cmd[0] = W25X_PageProgram;
    
#if W25QXX_4BYTE_ADDR_MODE
    cmd[1] = (addr >> 24) & 0xFF;
    cmd[2] = (addr >> 16) & 0xFF;
    cmd[3] = (addr >> 8) & 0xFF;
    cmd[4] = addr & 0xFF;
#else
    cmd[1] = (addr >> 16) & 0xFF;
    cmd[2] = (addr >> 8) & 0xFF;
    cmd[3] = addr & 0xFF;
#endif
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 1 + W25QXX_ADDR_BYTES);
    if(status == HAL_OK)
    {
        status = W25QXX_SPI_Transmit(pBuffer, numByteToWrite);
    }
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return status;
    
#elif USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_PageProgram;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = addr;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_1_LINE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    cmd.NbData = numByteToWrite;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
    
    if(W25QXX_QSPI_Transmit(pBuffer, W25QXX_TIMEOUT) != HAL_OK)
        return HAL_ERROR;
#endif
    
    return W25QXX_WaitForWriteEnd();
}

/**
 * @brief  写入数据(自动跨页)
 * @param  addr: 写入地址
 * @param  pBuffer: 数据缓冲区
 * @param  numByteToWrite: 写入字节数
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_WriteData(uint32_t addr, uint8_t* pBuffer, uint32_t numByteToWrite)
{
    uint32_t pageRemain;
    uint32_t writeAddr = addr;
    uint8_t* pData = pBuffer;
    uint32_t remainBytes = numByteToWrite;
    
    while(remainBytes > 0)
    {
        pageRemain = W25QXX_PAGE_SIZE - (writeAddr % W25QXX_PAGE_SIZE);
        uint32_t writeSize = (remainBytes < pageRemain) ? remainBytes : pageRemain;
        
        if(W25QXX_WritePage(writeAddr, pData, writeSize) != HAL_OK)
            return HAL_ERROR;
        
        writeAddr += writeSize;
        pData += writeSize;
        remainBytes -= writeSize;
    }
    
    return HAL_OK;
}

/**
 * @brief  扇区擦除
 * @param  sectorAddr: 扇区地址
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_EraseSector(uint32_t sectorAddr)
{
    uint32_t addr = sectorAddr * W25QXX_SECTOR_SIZE;
    
    if(W25QXX_WriteEnable() != HAL_OK)
        return HAL_ERROR;
    
#if USE_SPI_MODE
    uint8_t cmd[1 + W25QXX_ADDR_BYTES];
    cmd[0] = W25X_SectorErase;
    
#if W25QXX_4BYTE_ADDR_MODE
    cmd[1] = (addr >> 24) & 0xFF;
    cmd[2] = (addr >> 16) & 0xFF;
    cmd[3] = (addr >> 8) & 0xFF;
    cmd[4] = addr & 0xFF;
#else
    cmd[1] = (addr >> 16) & 0xFF;
    cmd[2] = (addr >> 8) & 0xFF;
    cmd[3] = addr & 0xFF;
#endif
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 1 + W25QXX_ADDR_BYTES);
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return status;
    
#elif USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_SectorErase;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = addr;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
#endif
    
    return W25QXX_WaitForWriteEnd();
}

/**
 * @brief  块擦除
 * @param  blockAddr: 块地址
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_EraseBlock(uint32_t blockAddr)
{
    uint32_t addr = blockAddr * W25QXX_BLOCK_SIZE;
    
    if(W25QXX_WriteEnable() != HAL_OK)
        return HAL_ERROR;
    
#if USE_SPI_MODE
    uint8_t cmd[1 + W25QXX_ADDR_BYTES];
    cmd[0] = W25X_BlockErase;
    
#if W25QXX_4BYTE_ADDR_MODE
    cmd[1] = (addr >> 24) & 0xFF;
    cmd[2] = (addr >> 16) & 0xFF;
    cmd[3] = (addr >> 8) & 0xFF;
    cmd[4] = addr & 0xFF;
#else
    cmd[1] = (addr >> 16) & 0xFF;
    cmd[2] = (addr >> 8) & 0xFF;
    cmd[3] = addr & 0xFF;
#endif
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(cmd, 1 + W25QXX_ADDR_BYTES);
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return status;
    
#elif USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_BlockErase;
    cmd.AddressMode = QSPI_ADDRESS_1_LINE;
    cmd.AddressSize = W25QXX_ADDR_SIZE;
    cmd.Address = addr;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
#endif
    
    return W25QXX_WaitForWriteEnd();
}

/**
 * @brief  整片擦除
 * @retval HAL状态
 */
HAL_StatusTypeDef W25QXX_EraseChip(void)
{
    if(W25QXX_WriteEnable() != HAL_OK)
        return HAL_ERROR;
    
#if USE_QSPI_MODE
    QSPI_CommandTypeDef cmd;
    
    cmd.InstructionMode = QSPI_INSTRUCTION_1_LINE;
    cmd.Instruction = W25X_ChipErase;
    cmd.AddressMode = QSPI_ADDRESS_NONE;
    cmd.AlternateByteMode = QSPI_ALTERNATE_BYTES_NONE;
    cmd.DataMode = QSPI_DATA_NONE;
    cmd.DummyCycles = 0;
    cmd.DdrMode = QSPI_DDR_MODE_DISABLE;
    cmd.DdrHoldHalfCycle = QSPI_DDR_HHC_ANALOG_DELAY;
    cmd.SIOOMode = QSPI_SIOO_INST_EVERY_CMD;
    
    if(W25QXX_QSPI_Command(&cmd) != HAL_OK)
        return HAL_ERROR;
    
#elif USE_SPI_MODE
    uint8_t cmd = W25X_ChipErase;
    
    W25QXX_CS_LOW();
    HAL_StatusTypeDef status = W25QXX_SPI_Transmit(&cmd, 1);
    W25QXX_CS_HIGH();
    
    if(status != HAL_OK)
        return status;
#endif
    
    return W25QXX_WaitForWriteEnd();
}

/* 私有函数实现 */
#if USE_QSPI_MODE
/**
 * @brief  QSPI命令发送
 * @param  cmd: 命令结构体
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_QSPI_Command(QSPI_CommandTypeDef* cmd)
{
    return HAL_QSPI_Command(&W25QXX_QSPI_HANDLE, cmd, W25QXX_TIMEOUT);
}

/**
 * @brief  QSPI数据发送
 * @param  pData: 数据指针
 * @param  timeout: 超时时间
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_QSPI_Transmit(uint8_t* pData, uint32_t timeout)
{
    return HAL_QSPI_Transmit(&W25QXX_QSPI_HANDLE, pData, timeout);
}

/**
 * @brief  QSPI数据接收
 * @param  pData: 数据指针
 * @param  timeout: 超时时间
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_QSPI_Receive(uint8_t* pData, uint32_t timeout)
{
    return HAL_QSPI_Receive(&W25QXX_QSPI_HANDLE, pData, timeout);
}

#elif USE_SPI_MODE
/**
 * @brief  SPI数据发送
 * @param  pData: 数据指针
 * @param  size: 数据大小
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_SPI_Transmit(uint8_t* pData, uint16_t size)
{
    return HAL_SPI_Transmit(&W25QXX_SPI_HANDLE, pData, size, W25QXX_TIMEOUT);
}

/**
 * @brief  SPI数据接收
 * @param  pData: 数据指针
 * @param  size: 数据大小
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_SPI_Receive(uint8_t* pData, uint16_t size)
{
    return HAL_SPI_Receive(&W25QXX_SPI_HANDLE, pData, size, W25QXX_TIMEOUT);
}

/**
 * @brief  SPI数据收发
 * @param  pTxData: 发送数据指针
 * @param  pRxData: 接收数据指针
 * @param  size: 数据大小
 * @retval HAL状态
 */
static HAL_StatusTypeDef W25QXX_SPI_TransmitReceive(uint8_t* pTxData, uint8_t* pRxData, uint16_t size)
{
    return HAL_SPI_TransmitReceive(&W25QXX_SPI_HANDLE, pTxData, pRxData, size, W25QXX_TIMEOUT);
}
#endif
