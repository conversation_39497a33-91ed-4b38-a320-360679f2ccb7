/**
 ******************************************************************************
 * @file    lcd.c
 * @brief   LCD显示驱动实现文件
 * @note    支持基于条件编译的单层/多层显示架构
 *          - 单层模式：仅使用LTDC层0进行显示
 *          - 多层模式：支持层0和层1的Alpha混合显示
 ******************************************************************************
 */

#include <stdlib.h>
#include <math.h>
#include "lcd.h"
#include "font.h"
#include "ltdc.h"
#include "dma2d.h"
#include "string.h"

/* ========================================================================================
 * 私有宏定义
 * ======================================================================================== */

#define LCD_TIMEOUT_VALUE               0x1FFFFF    // DMA2D操作超时值
#define LCD_COORDINATE_CHECK(x, y)      ((x) < g_lcd_device.width && (y) < g_lcd_device.height)
#define LCD_LAYER_CHECK(layer)          ((layer) < LCD_LAYER_COUNT)

/* ========================================================================================
 * 全局变量定义
 * ======================================================================================== */

// LCD设备结构体实例
lcd_device_t g_lcd_device;

// 前景色和背景色
uint32_t g_point_color = LCD_COLOR_BLACK;
uint32_t g_back_color = LCD_COLOR_WHITE;

/* 帧缓冲区定义 */
#ifdef LCD_MULTI_LAYER_ENABLE
// 多层模式：分配两个帧缓冲区
uint16_t g_lcd_framebuf_layer0[LCD_PANEL_HEIGHT][LCD_PANEL_WIDTH] __attribute__((section(".sdram_data")));
uint16_t g_lcd_framebuf_layer1[LCD_PANEL_HEIGHT][LCD_PANEL_WIDTH] __attribute__((section(".sdram_data")));

uint32_t *g_lcd_framebuf[LCD_LAYER_COUNT] = {
    (uint32_t *)&g_lcd_framebuf_layer0,
    (uint32_t *)&g_lcd_framebuf_layer1
};
#else
// 单层模式：仅分配一个帧缓冲区
uint16_t g_lcd_framebuf_layer0[LCD_PANEL_HEIGHT][LCD_PANEL_WIDTH] __attribute__((section(".sdram_data")));

uint32_t *g_lcd_framebuf[LCD_LAYER_COUNT] = {
    (uint32_t *)&g_lcd_framebuf_layer0
};
#endif

/* ========================================================================================
 * 私有函数声明
 * ======================================================================================== */

static uint8_t LCD_ValidateCoordinates(uint16_t x, uint16_t y);
#ifdef LCD_MULTI_LAYER_ENABLE
static uint8_t LCD_ValidateLayer(uint8_t layer);
#endif
static void LCD_DMA2D_Fill(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color);
static void LCD_DMA2D_ColorFill(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t *color_array);
static uint32_t LCD_CalculatePixelAddress(uint16_t x, uint16_t y, uint8_t layer);

/* ========================================================================================
 * 私有函数实现
 * ======================================================================================== */

/**
 * @brief  验证坐标是否有效
 * @param  x: X坐标
 * @param  y: Y坐标
 * @retval LCD_OK: 坐标有效, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
static uint8_t LCD_ValidateCoordinates(uint16_t x, uint16_t y)
{
    if (x >= g_lcd_device.width || y >= g_lcd_device.height) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }
    return LCD_OK;
}

#ifdef LCD_MULTI_LAYER_ENABLE
/**
 * @brief  验证层号是否有效
 * @param  layer: 层号
 * @retval LCD_OK: 层号有效, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
static uint8_t LCD_ValidateLayer(uint8_t layer)
{
    if (layer >= LCD_LAYER_COUNT) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }
    return LCD_OK;
}

/**
 * @brief  初始化第二层配置
 * @param  None
 * @retval None
 */
static void LCD_InitLayer1(void)
{
    LTDC_LayerCfgTypeDef layer_cfg = {0};

    // 配置层1参数
    layer_cfg.WindowX0 = 0;
    layer_cfg.WindowX1 = LCD_PANEL_WIDTH;
    layer_cfg.WindowY0 = 0;
    layer_cfg.WindowY1 = LCD_PANEL_HEIGHT;
    layer_cfg.PixelFormat = LTDC_PIXEL_FORMAT_RGB565;
    layer_cfg.Alpha = 255;  // 完全不透明
    layer_cfg.Alpha0 = 0;   // 完全透明
    layer_cfg.BlendingFactor1 = LTDC_BLENDING_FACTOR1_CA;  // 常量Alpha
    layer_cfg.BlendingFactor2 = LTDC_BLENDING_FACTOR2_CA;  // 常量Alpha
    layer_cfg.FBStartAdress = (uint32_t)&g_lcd_framebuf_layer1;  // 层1帧缓冲地址
    layer_cfg.ImageWidth = LCD_PANEL_WIDTH;
    layer_cfg.ImageHeight = LCD_PANEL_HEIGHT;
    layer_cfg.Backcolor.Blue = 0;
    layer_cfg.Backcolor.Green = 0;
    layer_cfg.Backcolor.Red = 0;

    // 配置层1
    HAL_LTDC_ConfigLayer(&hltdc, &layer_cfg, 1);

    // 默认禁用层1
    __HAL_LTDC_LAYER_DISABLE(&hltdc, 1);

    // 重新加载配置
    HAL_LTDC_Reload(&hltdc, LTDC_RELOAD_IMMEDIATE);
}
#endif

/**
 * @brief  初始化LCD参数（由LTDC初始化调用）
 * @param  None
 * @retval None
 */
void LTDC_ParameterInit(void)
{
    // 初始化LCD设备参数
    g_lcd_device.panel_width = LCD_PANEL_WIDTH;
    g_lcd_device.panel_height = LCD_PANEL_HEIGHT;
    g_lcd_device.width = g_lcd_device.panel_width;
    g_lcd_device.height = g_lcd_device.panel_height;
    g_lcd_device.hsync_width = 1;
    g_lcd_device.vsync_width = 3;
    g_lcd_device.h_back_porch = 46;
    g_lcd_device.v_back_porch = 23;
    g_lcd_device.h_front_porch = 40;
    g_lcd_device.v_front_porch = 13;
    g_lcd_device.pixel_size = LCD_PIXEL_SIZE;
    g_lcd_device.active_layer = LCD_DEFAULT_LAYER;
    g_lcd_device.orientation = 0;  // 默认竖屏模式

    // 设置显示方向
    LCD_SetOrientation(1);

    // 设置窗口位置和大小
    HAL_LTDC_SetWindowPosition(&hltdc, 0, 0, LCD_DEFAULT_LAYER);
    HAL_LTDC_SetWindowSize(&hltdc, LCD_PANEL_WIDTH, LCD_PANEL_HEIGHT, LCD_DEFAULT_LAYER);

    // 设置LTDC层的Alpha值（透明度）
    // Alpha = 255: 完全不透明（默认值）
    // Alpha = 0: 完全透明
    HAL_LTDC_SetAlpha(&hltdc, 255, LCD_DEFAULT_LAYER);

#ifdef LCD_MULTI_LAYER_ENABLE
    // 多层模式下初始化第二层
    LCD_InitLayer1();
    // 设置第二层的Alpha值
    HAL_LTDC_SetAlpha(&hltdc, 255, 1);
#endif

    // 清屏
    LCD_Clear(LCD_COLOR_WHITE);
}

/**
 * @brief  计算像素在帧缓冲区中的地址
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  layer: 层号
 * @retval 像素地址
 */
static uint32_t LCD_CalculatePixelAddress(uint16_t x, uint16_t y, uint8_t layer)
{
    uint32_t address;

    if (g_lcd_device.orientation == 0) {
        // 竖屏模式
        address = (uint32_t)g_lcd_framebuf[layer] +
                  g_lcd_device.pixel_size * (g_lcd_device.panel_width * (g_lcd_device.panel_height - x - 1) + y);
    } else {
        // 横屏模式
        address = (uint32_t)g_lcd_framebuf[layer] +
                  g_lcd_device.pixel_size * (g_lcd_device.panel_width * y + x);
    }

    return address;
}

/**
 * @brief  使用DMA2D进行矩形填充
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 填充颜色
 * @retval None
 */
static void LCD_DMA2D_Fill(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color)
{
    uint32_t psx, psy, pex, pey;
    uint32_t timeout = 0;
    uint16_t offline;
    uint32_t address;

    // 根据显示方向计算实际坐标
    if (g_lcd_device.orientation == 0) {
        // 竖屏模式
        psx = y1;
        psy = g_lcd_device.panel_height - x2 - 1;
        pex = y2;
        pey = g_lcd_device.panel_height - x1 - 1;
    } else {
        // 横屏模式
        psx = x1;
        psy = y1;
        pex = x2;
        pey = y2;
    }

    // 计算离线像素数和起始地址
    offline = g_lcd_device.panel_width - (pex - psx + 1);
    address = (uint32_t)g_lcd_framebuf[g_lcd_device.active_layer] +
              g_lcd_device.pixel_size * (g_lcd_device.panel_width * psy + psx);

    // 启用DMA2D时钟
    RCC->AHB1ENR |= 1 << 23;

    // 配置DMA2D为寄存器到内存模式
    DMA2D->CR = 3 << 16;  // 寄存器到内存模式
    DMA2D->OPFCCR = LCD_PIXEL_FORMAT_RGB565;  // 输出像素格式
    DMA2D->OOR = offline;  // 输出偏移
    DMA2D->CR &= ~(1 << 0);  // 停止DMA2D
    DMA2D->OMAR = address;  // 输出内存地址
    DMA2D->NLR = (pey - psy + 1) | ((pex - psx + 1) << 16);  // 行数和像素数
    DMA2D->OCOLR = color;  // 输出颜色

    // 启动DMA2D传输
    DMA2D->CR |= 1 << 0;

    // 等待传输完成
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > LCD_TIMEOUT_VALUE) break;
    }

    // 清除传输完成标志
    DMA2D->IFCR |= 1 << 1;
}

/**
 * @brief  使用DMA2D进行颜色数组填充
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color_array: 颜色数组指针
 * @retval None
 */
static void LCD_DMA2D_ColorFill(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t *color_array)
{
    uint32_t psx, psy, pex, pey;
    uint32_t timeout = 0;
    uint16_t offline;
    uint32_t address;

    // 根据显示方向计算实际坐标
    if (g_lcd_device.orientation == 0) {
        // 竖屏模式
        psx = y1;
        psy = g_lcd_device.panel_height - x2 - 1;
        pex = y2;
        pey = g_lcd_device.panel_height - x1 - 1;
    } else {
        // 横屏模式
        psx = x1;
        psy = y1;
        pex = x2;
        pey = y2;
    }

    // 计算离线像素数和起始地址
    offline = g_lcd_device.panel_width - (pex - psx + 1);
    address = (uint32_t)g_lcd_framebuf[g_lcd_device.active_layer] +
              g_lcd_device.pixel_size * (g_lcd_device.panel_width * psy + psx);

    // 启用DMA2D时钟
    RCC->AHB1ENR |= 1 << 23;

    // 配置DMA2D为内存到内存模式
    DMA2D->CR = 0 << 16;  // 内存到内存模式
    DMA2D->FGPFCCR = LCD_PIXEL_FORMAT;  // 前景层像素格式
    DMA2D->FGOR = 0;  // 前景层偏移
    DMA2D->OOR = offline;  // 输出偏移
    DMA2D->CR &= ~(1 << 0);  // 停止DMA2D
    DMA2D->FGMAR = (uint32_t)color_array;  // 前景层内存地址
    DMA2D->OMAR = address;  // 输出内存地址
    DMA2D->NLR = (pey - psy + 1) | ((pex - psx + 1) << 16);  // 行数和像素数

    // 启动DMA2D传输
    DMA2D->CR |= 1 << 0;

    // 等待传输完成
    while ((DMA2D->ISR & (1 << 1)) == 0) {
        timeout++;
        if (timeout > LCD_TIMEOUT_VALUE) break;
    }

    // 清除传输完成标志
    DMA2D->IFCR |= 1 << 1;
}

/* ========================================================================================
 * 公共API函数实现
 * ======================================================================================== */

/**
 * @brief  初始化LCD显示器
 * @param  None
 * @retval LCD_OK: 初始化成功, LCD_ERROR: 初始化失败
 */
uint8_t LCD_Init(void)
{
    // 启用LCD背光 (假设PD7控制背光)
    HAL_GPIO_WritePin(GPIOD, GPIO_PIN_7, GPIO_PIN_SET);

    // 初始化LCD参数
    LTDC_ParameterInit();


    // 清屏为白色
    LCD_Clear(LCD_COLOR_WHITE);

    return LCD_OK;
}

/**
 * @brief  设置显示方向
 * @param  orientation: 显示方向 (0: 竖屏, 1: 横屏)
 * @retval LCD_OK: 设置成功, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_SetOrientation(uint8_t orientation)
{
    if (orientation > 1) {
        return LCD_ERROR_INVALID_PARAM;
    }

    g_lcd_device.orientation = orientation;

    if (orientation == 0) {
        // 竖屏模式
        g_lcd_device.width = g_lcd_device.panel_height;
        g_lcd_device.height = g_lcd_device.panel_width;
    } else {
        // 横屏模式
        g_lcd_device.width = g_lcd_device.panel_width;
        g_lcd_device.height = g_lcd_device.panel_height;
    }

    return LCD_OK;
}

/**
 * @brief  开启或关闭LCD显示
 * @param  enable: 1-开启显示, 0-关闭显示
 * @retval None
 */
void LCD_DisplaySwitch(uint8_t enable)
{
    if (enable) {
        __HAL_LTDC_ENABLE(&hltdc);
    } else {
        __HAL_LTDC_DISABLE(&hltdc);
    }
}

/**
 * @brief  在指定位置绘制一个像素点
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 像素颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawPixel(uint16_t x, uint16_t y, uint32_t color)
{
    // 验证坐标
    if (LCD_ValidateCoordinates(x, y) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 计算像素地址并写入颜色
    uint32_t address = LCD_CalculatePixelAddress(x, y, g_lcd_device.active_layer);
    *(uint16_t *)address = (uint16_t)color;

    return LCD_OK;
}

/**
 * @brief  读取指定位置的像素颜色
 * @param  x: X坐标
 * @param  y: Y坐标
 * @retval 像素颜色值，如果坐标越界返回0
 */
uint32_t LCD_ReadPixel(uint16_t x, uint16_t y)
{
    // 验证坐标
    if (LCD_ValidateCoordinates(x, y) != LCD_OK) {
        return 0;
    }

    // 计算像素地址并读取颜色
    uint32_t address = LCD_CalculatePixelAddress(x, y, g_lcd_device.active_layer);
    return *(uint16_t *)address;
}

/**
 * @brief  绘制直线
 * @param  x1: 起点X坐标
 * @param  y1: 起点Y坐标
 * @param  x2: 终点X坐标
 * @param  y2: 终点Y坐标
 * @param  color: 线条颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawLine(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color)
{
    // 验证坐标
    if (LCD_ValidateCoordinates(x1, y1) != LCD_OK ||
        LCD_ValidateCoordinates(x2, y2) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // Bresenham直线算法
    int16_t dx = x2 - x1;
    int16_t dy = y2 - y1;
    int16_t ux = ((dx > 0) << 1) - 1;  // x的增量方向，取或-1
    int16_t uy = ((dy > 0) << 1) - 1;  // y的增量方向，取或-1
    int16_t x = x1, y = y1, eps;       // eps为累加误差

    dx = abs(dx);
    dy = abs(dy);

    if (dx > dy) {
        eps = dx / 2;
        for (int16_t i = 0; i <= dx; i++) {
            LCD_DrawPixel(x, y, color);
            eps += dy;
            if ((eps << 1) >= dx) {
                y += uy;
                eps -= dx;
            }
            x += ux;
        }
    } else {
        eps = dy / 2;
        for (int16_t i = 0; i <= dy; i++) {
            LCD_DrawPixel(x, y, color);
            eps += dx;
            if ((eps << 1) >= dy) {
                x += ux;
                eps -= dy;
            }
            y += uy;
        }
    }

    return LCD_OK;
}

/**
 * @brief  绘制矩形
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 矩形边框颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawRectangle(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color)
{
    // 验证坐标
    if (LCD_ValidateCoordinates(x1, y1) != LCD_OK ||
        LCD_ValidateCoordinates(x2, y2) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 绘制四条边
    LCD_DrawLine(x1, y1, x2, y1, color);  // 上边
    LCD_DrawLine(x1, y1, x1, y2, color);  // 左边
    LCD_DrawLine(x1, y2, x2, y2, color);  // 下边
    LCD_DrawLine(x2, y1, x2, y2, color);  // 右边

    return LCD_OK;
}

/**
 * @brief  绘制圆形
 * @param  x0: 圆心X坐标
 * @param  y0: 圆心Y坐标
 * @param  radius: 圆半径
 * @param  color: 圆形颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawCircle(uint16_t x0, uint16_t y0, uint8_t radius, uint32_t color)
{
    // 验证圆心坐标
    if (LCD_ValidateCoordinates(x0, y0) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // Bresenham圆形算法
    int16_t a = 0, b = radius;
    int16_t di = 3 - (radius << 1);  // 判断下个点位置的标志

    while (a <= b) {
        LCD_DrawPixel(x0 + a, y0 - b, color);  // 1
        LCD_DrawPixel(x0 + b, y0 - a, color);  // 2
        LCD_DrawPixel(x0 + b, y0 + a, color);  // 3
        LCD_DrawPixel(x0 + a, y0 + b, color);  // 4
        LCD_DrawPixel(x0 - a, y0 + b, color);  // 5
        LCD_DrawPixel(x0 - b, y0 + a, color);  // 6
        LCD_DrawPixel(x0 - a, y0 - b, color);  // 7
        LCD_DrawPixel(x0 - b, y0 - a, color);  // 8

        a++;
        // 使用Bresenham算法判断下一个点
        if (di < 0) {
            di += 4 * a + 6;
        } else {
            di += 10 + 4 * (a - b);
            b--;
        }
    }

    return LCD_OK;
}

/**
 * @brief  绘制填充圆形
 * @param  x0: 圆心X坐标
 * @param  y0: 圆心Y坐标
 * @param  radius: 圆半径
 * @param  color: 填充颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillCircle(uint16_t x0, uint16_t y0, uint8_t radius, uint32_t color)
{
    // 验证圆心坐标
    if (LCD_ValidateCoordinates(x0, y0) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 特殊情况处理
    if (radius == 0) {
        LCD_DrawPixel(x0, y0, color);
        return LCD_OK;
    }

    // 使用优化的扫描线填充算法
    int16_t r_squared = radius * radius;

    for (int16_t y = -radius; y <= radius; y++) {
        // 计算当前扫描线与圆的交点（避免使用sqrt）
        int16_t y_squared = y * y;
        int16_t x_max = 0;

        // 使用整数运算计算x的最大值
        for (int16_t x = 0; x <= radius; x++) {
            if (x * x + y_squared <= r_squared) {
                x_max = x;
            } else {
                break;
            }
        }

        // 绘制水平线段填充圆形
        int16_t py = y0 + y;
        if (py >= 0 && py < g_lcd_device.height) {
            for (int16_t x = -x_max; x <= x_max; x++) {
                int16_t px = x0 + x;
                if (px >= 0 && px < g_lcd_device.width) {
                    LCD_DrawPixel(px, py, color);
                }
            }
        }
    }

    return LCD_OK;
}

/**
 * @brief  用指定颜色填充矩形区域
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 填充颜色
 * @retval LCD_OK: 填充成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillRect(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color)
{
    // 验证坐标
    if (LCD_ValidateCoordinates(x1, y1) != LCD_OK ||
        LCD_ValidateCoordinates(x2, y2) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 确保坐标顺序正确
    if (x1 > x2) {
        uint16_t temp = x1; x1 = x2; x2 = temp;
    }
    if (y1 > y2) {
        uint16_t temp = y1; y1 = y2; y2 = temp;
    }

    // 使用DMA2D硬件加速填充
    LCD_DMA2D_Fill(x1, y1, x2, y2, color);

    return LCD_OK;
}

/**
 * @brief  用颜色数组填充矩形区域
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color_array: 颜色数组指针
 * @retval LCD_OK: 填充成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_FillRectWithArray(uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint16_t *color_array)
{
    // 验证参数
    if (color_array == NULL) {
        return LCD_ERROR_INVALID_PARAM;
    }

    // 验证坐标
    if (LCD_ValidateCoordinates(x1, y1) != LCD_OK ||
        LCD_ValidateCoordinates(x2, y2) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 确保坐标顺序正确
    if (x1 > x2) {
        uint16_t temp = x1; x1 = x2; x2 = temp;
    }
    if (y1 > y2) {
        uint16_t temp = y1; y1 = y2; y2 = temp;
    }

    // 使用DMA2D硬件加速填充
    LCD_DMA2D_ColorFill(x1, y1, x2, y2, color_array);

    return LCD_OK;
}

/**
 * @brief  清屏
 * @param  color: 清屏颜色
 * @retval None
 */
void LCD_Clear(uint32_t color)
{
    LCD_FillRect(0, 0, g_lcd_device.width - 1, g_lcd_device.height - 1, color);
}

/* 颜色设置函数 */
/**
 * @brief  同时设置前景色和背景色
 * @param  foreground_color: 前景色（画笔颜色）
 * @param  background_color: 背景色
 * @retval None
 * @note   这个函数在单层和多层模式下都可以使用
 */
void LCD_SetColor(uint32_t foreground_color, uint32_t background_color)
{
    g_point_color = foreground_color;
    g_back_color = background_color;
}

/**
 * @brief  设置前景色
 * @param  color: 前景色
 * @retval None
 */
void LCD_SetForegroundColor(uint32_t color)
{
    g_point_color = color;
}

/**
 * @brief  设置背景色
 * @param  color: 背景色
 * @retval None
 */
void LCD_SetBackgroundColor(uint32_t color)
{
    g_back_color = color;
}

/**
 * @brief  获取前景色
 * @retval 当前前景色
 */
uint32_t LCD_GetForegroundColor(void)
{
    return g_point_color;
}

/**
 * @brief  获取背景色
 * @retval 当前背景色
 */
uint32_t LCD_GetBackgroundColor(void)
{
    return g_back_color;
}

/* 文本显示辅助函数 */
/**
 * @brief  计算幂运算
 * @param  base: 底数
 * @param  exponent: 指数
 * @retval 计算结果
 */
static uint32_t LCD_Power(uint8_t base, uint8_t exponent)
{
    uint32_t result = 1;
    while (exponent--) {
        result *= base;
    }
    return result;
}

/**
 * @brief  显示单个字符
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  character: 要显示的字符
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  mode: 显示模式 (0: 非叠加模式, 1: 叠加模式)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowChar(uint16_t x, uint16_t y, uint8_t character, uint8_t font_size, uint8_t mode)
{
    uint8_t temp, t1, t;
    uint16_t y0 = y;
    uint8_t char_size = (font_size / 8 + ((font_size % 8) ? 1 : 0)) * (font_size / 2);

    // 验证坐标
    if (LCD_ValidateCoordinates(x, y) != LCD_OK) {
        return LCD_ERROR_OUT_OF_BOUNDS;
    }

    // 验证字体大小
    if (font_size != 12 && font_size != 16 && font_size != 24 && font_size != 32) {
        return LCD_ERROR_INVALID_PARAM;
    }

    character = character - ' ';  // 得到偏移后的值

    for (t = 0; t < char_size; t++) {
        // 根据字体大小选择字模
        if (font_size == 12) temp = asc2_1206[character][t];
        else if (font_size == 16) temp = asc2_1608[character][t];
        else if (font_size == 24) temp = asc2_2412[character][t];
        else if (font_size == 32) temp = asc2_3216[character][t];
        else return LCD_ERROR_INVALID_PARAM;

        for (t1 = 0; t1 < 8; t1++) {
            if (temp & 0x80) {
                LCD_DrawPixel(x, y, g_point_color);
            } else if (mode == 0) {
                LCD_DrawPixel(x, y, g_back_color);
            }
            temp <<= 1;
            y++;
            if (y >= g_lcd_device.height) return LCD_ERROR_OUT_OF_BOUNDS;
            if ((y - y0) == font_size) {
                y = y0;
                x++;
                if (x >= g_lcd_device.width) return LCD_ERROR_OUT_OF_BOUNDS;
                break;
            }
        }
    }

    return LCD_OK;
}

/**
 * @brief  显示数字
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  number: 要显示的数字
 * @param  length: 显示长度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowNumber(uint16_t x, uint16_t y, uint32_t number, uint8_t length, uint8_t font_size)
{
    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < length; t++) {
        temp = (number / LCD_Power(10, length - t - 1)) % 10;
        if (enshow == 0 && t < (length - 1)) {
            if (temp == 0) {
                if (LCD_ShowChar(x + (font_size / 2) * t, y, ' ', font_size, 0) != LCD_OK) {
                    return LCD_ERROR_OUT_OF_BOUNDS;
                }
                continue;
            } else {
                enshow = 1;
            }
        }
        if (LCD_ShowChar(x + (font_size / 2) * t, y, temp + '0', font_size, 0) != LCD_OK) {
            return LCD_ERROR_OUT_OF_BOUNDS;
        }
    }

    return LCD_OK;
}

/**
 * @brief  显示数字(扩展模式)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  number: 要显示的数字
 * @param  length: 显示长度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  mode: 显示模式 (bit7: 0-不填充前导0, 1-填充前导0; bit0: 0-非叠加, 1-叠加)
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowNumberEx(uint16_t x, uint16_t y, uint32_t number, uint8_t length, uint8_t font_size, uint8_t mode)
{
    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < length; t++) {
        temp = (number / LCD_Power(10, length - t - 1)) % 10;
        if (enshow == 0 && t < (length - 1)) {
            if (temp == 0) {
                if (mode & 0x80) {
                    // 填充前导0
                    if (LCD_ShowChar(x + (font_size / 2) * t, y, '0', font_size, mode & 0x01) != LCD_OK) {
                        return LCD_ERROR_OUT_OF_BOUNDS;
                    }
                } else {
                    // 不填充前导0，显示空格
                    if (LCD_ShowChar(x + (font_size / 2) * t, y, ' ', font_size, mode & 0x01) != LCD_OK) {
                        return LCD_ERROR_OUT_OF_BOUNDS;
                    }
                }
                continue;
            } else {
                enshow = 1;
            }
        }
        if (LCD_ShowChar(x + (font_size / 2) * t, y, temp + '0', font_size, mode & 0x01) != LCD_OK) {
            return LCD_ERROR_OUT_OF_BOUNDS;
        }
    }

    return LCD_OK;
}

/**
 * @brief  在指定区域内显示字符串
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  width: 显示区域宽度
 * @param  height: 显示区域高度
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  string: 要显示的字符串
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowString(uint16_t x, uint16_t y, uint16_t width, uint16_t height, uint8_t font_size, uint8_t *string)
{
    uint16_t x0 = x;
    uint16_t max_x = x + width;
    uint16_t max_y = y + height;

    if (string == NULL) {
        return LCD_ERROR_INVALID_PARAM;
    }

    while ((*string <= '~') && (*string >= ' ')) {
        if (x >= max_x) {
            x = x0;
            y += font_size;
        }
        if (y >= max_y) break;

        if (LCD_ShowChar(x, y, *string, font_size, 0) != LCD_OK) {
            return LCD_ERROR_OUT_OF_BOUNDS;
        }

        x += font_size / 2;
        string++;
    }

    return LCD_OK;
}

/**
 * @brief  显示字符串(简化版本)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  font_size: 字体大小 (12, 16, 24, 32)
 * @param  string: 要显示的字符串
 * @retval LCD_OK: 显示成功, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界, LCD_ERROR_INVALID_PARAM: 参数错误
 */
uint8_t LCD_ShowStringSimple(uint16_t x, uint16_t y, uint8_t font_size, char *string)
{
    uint16_t x0 = x;
    uint16_t width = x + strlen(string) * font_size / 2;
    uint16_t height = y + font_size;

    if (string == NULL) {
        return LCD_ERROR_INVALID_PARAM;
    }

    while ((*string <= '~') && (*string >= ' ')) {
        if (x >= width) {
            x = x0;
            y += font_size;
        }
        if (y >= height) break;

        if (LCD_ShowChar(x, y, *string, font_size, 0) != LCD_OK) {
            return LCD_ERROR_OUT_OF_BOUNDS;
        }

        x += font_size / 2;
        string++;
    }

    return LCD_OK;
}

#ifdef LCD_MULTI_LAYER_ENABLE
/* ========================================================================================
 * 多层显示函数实现 (仅在多层模式下可用)
 * ======================================================================================== */

/**
 * @brief  切换当前操作层
 * @param  layer: 目标层 (0 或 1)
 * @retval LCD_OK: 切换成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_SwitchLayer(uint8_t layer)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    g_lcd_device.active_layer = layer;
    return LCD_OK;
}

/**
 * @brief  清除指定层
 * @param  layer: 目标层 (0 或 1)
 * @param  color: 清除颜色
 * @retval LCD_OK: 清除成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_ClearLayer(uint8_t layer, uint32_t color)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    uint8_t old_layer = g_lcd_device.active_layer;
    g_lcd_device.active_layer = layer;
    LCD_Clear(color);
    g_lcd_device.active_layer = old_layer;

    return LCD_OK;
}

/**
 * @brief  在指定层填充矩形区域
 * @param  layer: 目标层 (0 或 1)
 * @param  x1: 左上角X坐标
 * @param  y1: 左上角Y坐标
 * @param  x2: 右下角X坐标
 * @param  y2: 右下角Y坐标
 * @param  color: 填充颜色
 * @retval LCD_OK: 填充成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_FillRectLayer(uint8_t layer, uint16_t x1, uint16_t y1, uint16_t x2, uint16_t y2, uint32_t color)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    uint8_t old_layer = g_lcd_device.active_layer;
    g_lcd_device.active_layer = layer;
    uint8_t result = LCD_FillRect(x1, y1, x2, y2, color);
    g_lcd_device.active_layer = old_layer;

    return result;
}

/**
 * @brief  在指定层绘制像素点
 * @param  layer: 目标层 (0 或 1)
 * @param  x: X坐标
 * @param  y: Y坐标
 * @param  color: 像素颜色
 * @retval LCD_OK: 绘制成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层, LCD_ERROR_OUT_OF_BOUNDS: 坐标越界
 */
uint8_t LCD_DrawPixelLayer(uint8_t layer, uint16_t x, uint16_t y, uint32_t color)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    uint8_t old_layer = g_lcd_device.active_layer;
    g_lcd_device.active_layer = layer;
    uint8_t result = LCD_DrawPixel(x, y, color);
    g_lcd_device.active_layer = old_layer;

    return result;
}

/**
 * @brief  设置层的透明度
 * @param  layer: 目标层 (0 或 1)
 * @param  alpha: 透明度值 (0-255, 0为完全透明, 255为完全不透明)
 * @retval LCD_OK: 设置成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_SetLayerAlpha(uint8_t layer, uint8_t alpha)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    // 设置LTDC层的Alpha值
    HAL_LTDC_SetAlpha(&hltdc, alpha, layer);

    return LCD_OK;
}

/**
 * @brief  启用或禁用指定层
 * @param  layer: 目标层 (0 或 1)
 * @param  enable: 1-启用, 0-禁用
 * @retval LCD_OK: 设置成功, LCD_ERROR_LAYER_NOT_SUPPORTED: 不支持的层
 */
uint8_t LCD_EnableLayer(uint8_t layer, uint8_t enable)
{
    if (LCD_ValidateLayer(layer) != LCD_OK) {
        return LCD_ERROR_LAYER_NOT_SUPPORTED;
    }

    if (enable) {
        __HAL_LTDC_LAYER_ENABLE(&hltdc, layer);
    } else {
        __HAL_LTDC_LAYER_DISABLE(&hltdc, layer);
    }

    // 重新加载LTDC配置使更改生效
    HAL_LTDC_Reload(&hltdc, LTDC_RELOAD_IMMEDIATE);

    return LCD_OK;
}

#endif /* LCD_MULTI_LAYER_ENABLE */